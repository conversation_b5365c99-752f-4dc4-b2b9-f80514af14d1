import type { CollectionConfig } from 'payload'
import { admins, adminsAndUser, anyone } from '../access/index'
import { checkRole } from '../access/checkRole'

export const Products: CollectionConfig = {
  slug: 'products',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'supplier', 'price', 'status', 'createdAt'],
    listSearchableFields: ['name', 'description', 'sku'],
    group: '产品管理',
    pagination: {
      defaultLimit: 20,
    },
  },
  access: {
    read: anyone,
    create: adminsAndUser,
    update: ({ req: { user } }) => {
      if (checkRole(['admin'], user)) return true
      return {
        supplier: {
          equals: user?.id,
        },
      }
    },
    delete: ({ req: { user } }) => {
      if (checkRole(['admin'], user)) return true
      return {
        supplier: {
          equals: user?.id,
        },
      }
    },
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 自动设置供应商为当前用户
          if (!data.supplier && req.user) {
            data.supplier = req.user.id
          }
          // 生成SKU
          if (!data.sku) {
            data.sku = `PRD-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`
          }
          // 设置默认状态
          data.status = data.status || 'draft'
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'name',
      label: '产品名称',
      type: 'text',
      required: true,
      admin: {
        placeholder: '请输入产品名称',
      },
    },
    {
      name: 'slug',
      label: 'URL别名',
      type: 'text',
      unique: true,
      admin: {
        description: '用于URL的唯一标识符',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.name) {
              return data.name
                .toLowerCase()
                .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'description',
      label: '产品描述',
      type: 'richText',
      required: true,
      admin: {
        description: '详细的产品描述',
      },
    },
    {
      name: 'shortDescription',
      label: '简短描述',
      type: 'textarea',
      maxLength: 200,
      admin: {
        description: '用于列表显示的简短描述',
      },
    },
    {
      name: 'sku',
      label: 'SKU',
      type: 'text',
      unique: true,
      admin: {
        description: '产品唯一标识码',
        readOnly: true,
      },
    },
    {
      name: 'category',
      label: '产品分类',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      hasMany: false,
    },
    {
      name: 'subcategories',
      label: '子分类',
      type: 'relationship',
      relationTo: 'categories',
      hasMany: true,
      admin: {
        description: '可选择多个子分类',
      },
    },
    {
      name: 'supplier',
      label: '供应商',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: '产品供应商',
      },
    },
    {
      name: 'images',
      label: '产品图片',
      type: 'array',
      minRows: 1,
      maxRows: 10,
      fields: [
        {
          name: 'image',
          label: '图片',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'alt',
          label: '图片描述',
          type: 'text',
        },
        {
          name: 'isPrimary',
          label: '主图',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'pricing',
      label: '价格信息',
      type: 'group',
      fields: [
        {
          name: 'currency',
          label: '货币',
          type: 'select',
          defaultValue: 'CNY',
          options: [
            { label: '人民币 (CNY)', value: 'CNY' },
            { label: '美元 (USD)', value: 'USD' },
            { label: '欧元 (EUR)', value: 'EUR' },
          ],
        },
        {
          name: 'minPrice',
          label: '最低价格',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'maxPrice',
          label: '最高价格',
          type: 'number',
          min: 0,
          admin: {
            description: '如果是固定价格，可以留空',
          },
        },
        {
          name: 'priceUnit',
          label: '价格单位',
          type: 'text',
          defaultValue: '件',
          admin: {
            placeholder: '如：件、公斤、米等',
          },
        },
        {
          name: 'negotiable',
          label: '价格可议',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'inventory',
      label: '库存信息',
      type: 'group',
      fields: [
        {
          name: 'stock',
          label: '库存数量',
          type: 'number',
          defaultValue: 0,
          min: 0,
        },
        {
          name: 'minOrderQuantity',
          label: '最小起订量',
          type: 'number',
          defaultValue: 1,
          min: 1,
        },
        {
          name: 'maxOrderQuantity',
          label: '最大订购量',
          type: 'number',
          min: 1,
        },
        {
          name: 'stockStatus',
          label: '库存状态',
          type: 'select',
          defaultValue: 'in_stock',
          options: [
            { label: '有库存', value: 'in_stock' },
            { label: '缺货', value: 'out_of_stock' },
            { label: '预订', value: 'pre_order' },
          ],
        },
      ],
    },
    {
      name: 'specifications',
      label: '产品规格',
      type: 'array',
      fields: [
        {
          name: 'name',
          label: '规格名称',
          type: 'text',
          required: true,
        },
        {
          name: 'value',
          label: '规格值',
          type: 'text',
          required: true,
        },
        {
          name: 'unit',
          label: '单位',
          type: 'text',
        },
      ],
    },
    {
      name: 'shipping',
      label: '物流信息',
      type: 'group',
      fields: [
        {
          name: 'weight',
          label: '重量 (kg)',
          type: 'number',
          min: 0,
        },
        {
          name: 'dimensions',
          label: '尺寸',
          type: 'group',
          fields: [
            {
              name: 'length',
              label: '长度 (cm)',
              type: 'number',
              min: 0,
            },
            {
              name: 'width',
              label: '宽度 (cm)',
              type: 'number',
              min: 0,
            },
            {
              name: 'height',
              label: '高度 (cm)',
              type: 'number',
              min: 0,
            },
          ],
        },
        {
          name: 'shippingTime',
          label: '发货时间',
          type: 'text',
          admin: {
            placeholder: '如：3-5个工作日',
          },
        },
        {
          name: 'shippingMethods',
          label: '配送方式',
          type: 'select',
          hasMany: true,
          options: [
            { label: '快递', value: 'express' },
            { label: '物流', value: 'logistics' },
            { label: '自提', value: 'pickup' },
            { label: '海运', value: 'sea' },
            { label: '空运', value: 'air' },
          ],
        },
      ],
    },
    {
      name: 'tags',
      label: '标签',
      type: 'text',
      hasMany: true,
      admin: {
        description: '用于搜索和分类的标签',
      },
    },
    {
      name: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'draft',
      options: [
        { label: '草稿', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已下架', value: 'archived' },
        { label: '待审核', value: 'pending' },
      ],
    },
    {
      name: 'featured',
      label: '推荐产品',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: '是否在首页推荐',
      },
    },
    {
      name: 'trending',
      label: '热门产品',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'accessLevel',
      label: '访问级别',
      type: 'select',
      defaultValue: 'public',
      options: [
        { label: '公开', value: 'public' },
        { label: 'VIP专享', value: 'vip' },
        { label: '企业版专享', value: 'enterprise' },
      ],
      admin: {
        description: '控制产品的访问权限',
      },
    },
    {
      name: 'seo',
      label: 'SEO设置',
      type: 'group',
      fields: [
        {
          name: 'title',
          label: 'SEO标题',
          type: 'text',
          maxLength: 60,
        },
        {
          name: 'description',
          label: 'SEO描述',
          type: 'textarea',
          maxLength: 160,
        },
        {
          name: 'keywords',
          label: '关键词',
          type: 'text',
          hasMany: true,
        },
      ],
    },
    {
      name: 'analytics',
      label: '统计数据',
      type: 'group',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'views',
          label: '浏览次数',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'inquiries',
          label: '询价次数',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'favorites',
          label: '收藏次数',
          type: 'number',
          defaultValue: 0,
        },
      ],
    },
  ],
  timestamps: true,
}
