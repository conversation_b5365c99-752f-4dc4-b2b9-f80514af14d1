'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  LayoutDashboard,
  Users,
  Package,
  Building,
  ShoppingCart,
  CreditCard,
  Star,
  BarChart3,
  Settings,
  FileText,
  Image,
  Navigation,
  Globe,
  Shield,
  Bell,
  HelpCircle,
  LogOut
} from 'lucide-react'

interface NavItem {
  label: string
  href: string
  icon: React.ElementType
  badge?: string
  children?: NavItem[]
}

const navigationItems: NavItem[] = [
  {
    label: '仪表板',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    label: '用户管理',
    href: '/admin/collections/users',
    icon: Users,
    children: [
      { label: '所有用户', href: '/admin/collections/users', icon: Users },
      { label: '用户角色', href: '/admin/collections/users?filter=role', icon: Shield },
      { label: 'VIP用户', href: '/admin/collections/users?filter=vipLevel', icon: Star },
    ],
  },
  {
    label: '产品管理',
    href: '/admin/collections/products',
    icon: Package,
    children: [
      { label: '所有产品', href: '/admin/collections/products', icon: Package },
      { label: '产品分类', href: '/admin/collections/categories', icon: FileText },
      { label: '待审核', href: '/admin/collections/products?filter=status:pending', icon: Package },
    ],
  },
  {
    label: '企业管理',
    href: '/admin/collections/companies',
    icon: Building,
    children: [
      { label: '所有企业', href: '/admin/collections/companies', icon: Building },
      { label: '认证申请', href: '/admin/collections/companies?filter=verificationStatus:pending', icon: Shield },
      { label: '企业统计', href: '/admin/companies/stats', icon: BarChart3 },
    ],
  },
  {
    label: '订单管理',
    href: '/admin/collections/orders',
    icon: ShoppingCart,
    children: [
      { label: '所有订单', href: '/admin/collections/orders', icon: ShoppingCart },
      { label: '待处理', href: '/admin/collections/orders?filter=status:pending', icon: ShoppingCart },
      { label: '已完成', href: '/admin/collections/orders?filter=status:completed', icon: ShoppingCart },
    ],
  },
  {
    label: '支付管理',
    href: '/admin/collections/payments',
    icon: CreditCard,
    children: [
      { label: '支付记录', href: '/admin/collections/payments', icon: CreditCard },
      { label: 'VIP订阅', href: '/admin/collections/subscriptions', icon: Star },
      { label: '积分交易', href: '/admin/collections/points-transactions', icon: Star },
    ],
  },
  {
    label: '内容管理',
    href: '/admin/collections/media',
    icon: Image,
    children: [
      { label: '媒体文件', href: '/admin/collections/media', icon: Image },
      { label: '访问日志', href: '/admin/collections/access-logs', icon: FileText },
    ],
  },
  {
    label: '系统设置',
    href: '/admin/globals/settings',
    icon: Settings,
    children: [
      { label: '基础设置', href: '/admin/globals/settings', icon: Settings },
      { label: '导航管理', href: '/admin/globals/navigation', icon: Navigation },
      { label: '站点配置', href: '/admin/globals/site-config', icon: Globe },
    ],
  },
  {
    label: '数据统计',
    href: '/admin/analytics',
    icon: BarChart3,
  },
]

interface CustomNavigationProps {
  className?: string
}

export default function CustomNavigation({ className }: CustomNavigationProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = React.useState<string[]>([])

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => 
      prev.includes(href) 
        ? prev.filter(item => item !== href)
        : [...prev, href]
    )
  }

  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === '/admin'
    }
    return pathname.startsWith(href)
  }

  const isExpanded = (href: string) => {
    return expandedItems.includes(href) || pathname.startsWith(href)
  }

  const NavItemComponent = ({ item, level = 0 }: { item: NavItem; level?: number }) => {
    const hasChildren = item.children && item.children.length > 0
    const active = isActive(item.href)
    const expanded = isExpanded(item.href)

    return (
      <div key={item.href}>
        <div
          className={cn(
            'flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer',
            level > 0 && 'ml-4 pl-6',
            active 
              ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100' 
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
          )}
          onClick={() => hasChildren ? toggleExpanded(item.href) : null}
        >
          <Link 
            href={item.href}
            className="flex items-center flex-1"
            onClick={(e) => hasChildren && e.preventDefault()}
          >
            <item.icon className="w-5 h-5 mr-3" />
            <span>{item.label}</span>
            {item.badge && (
              <span className="ml-2 px-2 py-1 text-xs bg-red-500 text-white rounded-full">
                {item.badge}
              </span>
            )}
          </Link>
          {hasChildren && (
            <div className="ml-2">
              <svg
                className={cn(
                  'w-4 h-4 transition-transform',
                  expanded ? 'rotate-90' : 'rotate-0'
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          )}
        </div>
        
        {hasChildren && expanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => (
              <NavItemComponent key={child.href} item={child} level={level + 1} />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <nav className={cn('space-y-1', className)}>
      {/* 用户信息 */}
      <div className="px-3 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white font-semibold">管</span>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              管理员
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <div className="px-3 py-2">
        <div className="space-y-1">
          {navigationItems.map(item => (
            <NavItemComponent key={item.href} item={item} />
          ))}
        </div>
      </div>

      {/* 底部操作 */}
      <div className="px-3 py-4 border-t border-gray-200 dark:border-gray-700 mt-auto">
        <div className="space-y-1">
          <Link
            href="/admin/help"
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
          >
            <HelpCircle className="w-5 h-5 mr-3" />
            帮助中心
          </Link>
          
          <Link
            href="/admin/notifications"
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
          >
            <Bell className="w-5 h-5 mr-3" />
            通知中心
            <span className="ml-auto px-2 py-1 text-xs bg-red-500 text-white rounded-full">
              3
            </span>
          </Link>
          
          <button
            className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
            onClick={() => {
              // 处理登出逻辑
              console.log('Logout clicked')
            }}
          >
            <LogOut className="w-5 h-5 mr-3" />
            退出登录
          </button>
        </div>
      </div>
    </nav>
  )
}
