import type { CollectionConfig } from 'payload'
import { admins, adminsAndUser } from '../access/index'

export const Subscriptions: CollectionConfig = {
  slug: 'subscriptions',
  admin: {
    useAsTitle: 'subscriptionId',
    defaultColumns: ['subscriptionId', 'user', 'plan', 'status', 'startDate', 'endDate'],
    listSearchableFields: ['subscriptionId'],
    group: '订阅管理',
  },
  access: {
    read: adminsAndUser,
    create: adminsAndUser,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role?.includes('admin')) return true
      
      return {
        user: {
          equals: user.id,
        },
      }
    },
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 生成订阅ID
          if (!data.subscriptionId) {
            const timestamp = Date.now()
            const random = Math.random().toString(36).substr(2, 6).toUpperCase()
            data.subscriptionId = `SUB-${timestamp}-${random}`
          }
          
          // 设置默认状态
          data.status = data.status || 'active'
          
          // 设置用户
          if (!data.user && req.user) {
            data.user = req.user.id
          }
          
          // 设置开始时间
          if (!data.startDate) {
            data.startDate = new Date().toISOString()
          }
          
          // 根据计划设置结束时间
          if (!data.endDate && data.plan && data.startDate) {
            const startDate = new Date(data.startDate)
            const endDate = new Date(startDate)
            
            switch (data.billingCycle) {
              case 'monthly':
                endDate.setMonth(endDate.getMonth() + 1)
                break
              case 'quarterly':
                endDate.setMonth(endDate.getMonth() + 3)
                break
              case 'yearly':
                endDate.setFullYear(endDate.getFullYear() + 1)
                break
              default:
                endDate.setMonth(endDate.getMonth() + 1)
            }
            
            data.endDate = endDate.toISOString()
          }
        }
        return data
      },
    ],
    afterChange: [
      async ({ doc, req, operation }) => {
        if (operation === 'create' || operation === 'update') {
          // 更新用户的VIP等级
          if (doc.status === 'active' && doc.user) {
            await req.payload.update({
              collection: 'users',
              id: doc.user,
              data: {
                vipLevel: doc.plan,
              },
            })
          }
        }
      },
    ],
  },
  fields: [
    {
      name: 'subscriptionId',
      label: '订阅ID',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'user',
      label: '用户',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'plan',
      label: 'VIP计划',
      type: 'select',
      required: true,
      options: [
        { label: '专业版', value: 'Pro' },
        { label: '企业版', value: 'Enterprise' },
      ],
    },
    {
      name: 'billingCycle',
      label: '计费周期',
      type: 'select',
      required: true,
      defaultValue: 'monthly',
      options: [
        { label: '月付', value: 'monthly' },
        { label: '季付', value: 'quarterly' },
        { label: '年付', value: 'yearly' },
      ],
    },
    {
      name: 'price',
      label: '价格',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'currency',
      label: '货币',
      type: 'select',
      defaultValue: 'CNY',
      options: [
        { label: '人民币 (CNY)', value: 'CNY' },
        { label: '美元 (USD)', value: 'USD' },
        { label: '欧元 (EUR)', value: 'EUR' },
      ],
    },
    {
      name: 'status',
      label: '订阅状态',
      type: 'select',
      defaultValue: 'active',
      options: [
        { label: '激活', value: 'active' },
        { label: '暂停', value: 'paused' },
        { label: '已取消', value: 'cancelled' },
        { label: '已过期', value: 'expired' },
        { label: '待支付', value: 'pending' },
      ],
    },
    {
      name: 'startDate',
      label: '开始日期',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'endDate',
      label: '结束日期',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'autoRenew',
      label: '自动续费',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'renewalDate',
      label: '下次续费日期',
      type: 'date',
      admin: {
        condition: (data) => data.autoRenew,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'trialPeriod',
      label: '试用期',
      type: 'group',
      fields: [
        {
          name: 'isTrial',
          label: '是否试用',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'trialDays',
          label: '试用天数',
          type: 'number',
          min: 0,
          admin: {
            condition: (data, siblingData) => siblingData?.isTrial,
          },
        },
        {
          name: 'trialEndDate',
          label: '试用结束日期',
          type: 'date',
          admin: {
            condition: (data, siblingData) => siblingData?.isTrial,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    {
      name: 'features',
      label: '订阅功能',
      type: 'array',
      fields: [
        {
          name: 'feature',
          label: '功能名称',
          type: 'text',
          required: true,
        },
        {
          name: 'limit',
          label: '限制数量',
          type: 'number',
          admin: {
            description: '-1表示无限制',
          },
        },
        {
          name: 'used',
          label: '已使用',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'discounts',
      label: '折扣信息',
      type: 'array',
      fields: [
        {
          name: 'type',
          label: '折扣类型',
          type: 'select',
          options: [
            { label: '百分比', value: 'percentage' },
            { label: '固定金额', value: 'fixed' },
          ],
        },
        {
          name: 'value',
          label: '折扣值',
          type: 'number',
          min: 0,
        },
        {
          name: 'code',
          label: '优惠码',
          type: 'text',
        },
        {
          name: 'description',
          label: '描述',
          type: 'text',
        },
      ],
    },
    {
      name: 'paymentHistory',
      label: '支付历史',
      type: 'array',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'payment',
          label: '支付记录',
          type: 'relationship',
          relationTo: 'payments',
        },
        {
          name: 'amount',
          label: '金额',
          type: 'number',
        },
        {
          name: 'date',
          label: '支付日期',
          type: 'date',
        },
        {
          name: 'status',
          label: '状态',
          type: 'text',
        },
      ],
    },
    {
      name: 'cancellationInfo',
      label: '取消信息',
      type: 'group',
      admin: {
        condition: (data) => data.status === 'cancelled',
      },
      fields: [
        {
          name: 'cancelledAt',
          label: '取消时间',
          type: 'date',
        },
        {
          name: 'reason',
          label: '取消原因',
          type: 'textarea',
        },
        {
          name: 'cancelledBy',
          label: '取消人',
          type: 'relationship',
          relationTo: 'users',
        },
        {
          name: 'refundAmount',
          label: '退款金额',
          type: 'number',
          min: 0,
        },
      ],
    },
    {
      name: 'notes',
      label: '备注',
      type: 'textarea',
    },
    {
      name: 'internalNotes',
      label: '内部备注',
      type: 'textarea',
      access: {
        read: admins,
        update: admins,
      },
    },
    {
      name: 'metadata',
      label: '元数据',
      type: 'json',
      admin: {
        description: '存储额外的订阅相关数据',
      },
    },
  ],
  timestamps: true,
}
