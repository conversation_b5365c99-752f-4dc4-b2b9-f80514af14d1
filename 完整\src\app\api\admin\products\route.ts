import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../payload.config'

// 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''
    const featured = searchParams.get('featured') || ''
    const priceMin = searchParams.get('priceMin') || ''
    const priceMax = searchParams.get('priceMax') || ''
    const sortField = searchParams.get('sortField') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.or = [
        { title: { contains: search } },
        { description: { contains: search } },
        { sku: { contains: search } },
        { tags: { contains: search } }
      ]
    }
    
    if (category) {
      where.category = { equals: category }
    }
    
    if (status) {
      where.status = { equals: status }
    }
    
    if (featured) {
      where.featured = { equals: featured === 'true' }
    }

    if (priceMin || priceMax) {
      where.price = {}
      if (priceMin) where.price.greater_than_equal = parseFloat(priceMin)
      if (priceMax) where.price.less_than_equal = parseFloat(priceMax)
    }

    // 查询产品
    const products = await payload.find({
      collection: 'products',
      where,
      page,
      limit,
      sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
      depth: 2,
    })

    // 统计数据
    const totalProducts = await payload.count({
      collection: 'products',
      where: {},
    })

    const publishedProducts = await payload.count({
      collection: 'products',
      where: { status: { equals: 'published' } },
    })

    const draftProducts = await payload.count({
      collection: 'products',
      where: { status: { equals: 'draft' } },
    })

    const featuredProducts = await payload.count({
      collection: 'products',
      where: { featured: { equals: true } },
    })

    const lowStockProducts = await payload.count({
      collection: 'products',
      where: { 
        and: [
          { inventory: { less_than: 10 } },
          { status: { equals: 'published' } }
        ]
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        products: products.docs,
        pagination: {
          page: products.page,
          limit: products.limit,
          totalPages: products.totalPages,
          totalDocs: products.totalDocs,
          hasNextPage: products.hasNextPage,
          hasPrevPage: products.hasPrevPage,
        },
        stats: {
          total: totalProducts.totalDocs,
          published: publishedProducts.totalDocs,
          draft: draftProducts.totalDocs,
          featured: featuredProducts.totalDocs,
          lowStock: lowStockProducts.totalDocs,
        },
      },
    })
  } catch (error) {
    console.error('获取产品列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取产品列表失败' },
      { status: 500 }
    )
  }
}

// 创建新产品
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // 验证必填字段
    if (!body.title || !body.price) {
      return NextResponse.json(
        { success: false, error: '产品标题和价格为必填项' },
        { status: 400 }
      )
    }

    // 检查SKU是否已存在
    if (body.sku) {
      const existingProduct = await payload.find({
        collection: 'products',
        where: { sku: { equals: body.sku } },
        limit: 1,
      })

      if (existingProduct.docs.length > 0) {
        return NextResponse.json(
          { success: false, error: '该SKU已存在' },
          { status: 400 }
        )
      }
    }

    // 创建产品
    const product = await payload.create({
      collection: 'products',
      data: {
        title: body.title,
        description: body.description || '',
        content: body.content || '',
        price: parseFloat(body.price),
        originalPrice: body.originalPrice ? parseFloat(body.originalPrice) : null,
        sku: body.sku || `PRD-${Date.now()}`,
        category: body.category || null,
        tags: body.tags || [],
        images: body.images || [],
        gallery: body.gallery || [],
        inventory: parseInt(body.inventory || '0'),
        minOrder: parseInt(body.minOrder || '1'),
        maxOrder: body.maxOrder ? parseInt(body.maxOrder) : null,
        weight: body.weight ? parseFloat(body.weight) : null,
        dimensions: body.dimensions || null,
        status: body.status || 'draft',
        featured: body.featured || false,
        seoTitle: body.seoTitle || body.title,
        seoDescription: body.seoDescription || body.description,
        seoKeywords: body.seoKeywords || [],
        specifications: body.specifications || [],
        variants: body.variants || [],
        shipping: body.shipping || {
          free: false,
          weight: 0,
          dimensions: null,
          methods: [],
        },
        vipOnly: body.vipOnly || false,
        pointsRequired: body.pointsRequired || 0,
        downloadable: body.downloadable || false,
        downloadFiles: body.downloadFiles || [],
      },
    })

    return NextResponse.json({
      success: true,
      data: product,
      message: '产品创建成功',
    })
  } catch (error) {
    console.error('创建产品失败:', error)
    return NextResponse.json(
      { success: false, error: '创建产品失败' },
      { status: 500 }
    )
  }
}

// 批量操作产品
export async function PATCH(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { action, productIds, data } = body

    if (!action || !productIds || !Array.isArray(productIds)) {
      return NextResponse.json(
        { success: false, error: '参数错误' },
        { status: 400 }
      )
    }

    const results = []

    for (const productId of productIds) {
      try {
        let result
        
        switch (action) {
          case 'publish':
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { status: 'published' },
            })
            break
            
          case 'unpublish':
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { status: 'draft' },
            })
            break
            
          case 'feature':
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { featured: true },
            })
            break
            
          case 'unfeature':
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { featured: false },
            })
            break
            
          case 'delete':
            result = await payload.delete({
              collection: 'products',
              id: productId,
            })
            break
            
          case 'updateCategory':
            if (!data?.category) {
              throw new Error('分类不能为空')
            }
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { category: data.category },
            })
            break
            
          case 'updatePrice':
            if (!data?.price || isNaN(data.price)) {
              throw new Error('价格无效')
            }
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { price: parseFloat(data.price) },
            })
            break
            
          case 'updateInventory':
            if (data?.inventory === undefined || isNaN(data.inventory)) {
              throw new Error('库存数量无效')
            }
            result = await payload.update({
              collection: 'products',
              id: productId,
              data: { inventory: parseInt(data.inventory) },
            })
            break
            
          default:
            throw new Error(`不支持的操作: ${action}`)
        }
        
        results.push({ productId, success: true, data: result })
      } catch (error) {
        results.push({ 
          productId, 
          success: false, 
          error: error instanceof Error ? error.message : '操作失败' 
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: results,
      message: `操作完成: ${successCount} 成功, ${failCount} 失败`,
    })
  } catch (error) {
    console.error('批量操作失败:', error)
    return NextResponse.json(
      { success: false, error: '批量操作失败' },
      { status: 500 }
    )
  }
}
