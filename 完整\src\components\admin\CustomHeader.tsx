'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Search,
  Bell,
  Settings,
  User,
  Moon,
  Sun,
  Menu,
  Globe,
  RefreshCw,
  Plus,
  Filter,
  Download,
  Upload
} from 'lucide-react'

interface CustomHeaderProps {
  title?: string
  subtitle?: string
  onMenuToggle?: () => void
  showSearch?: boolean
  showActions?: boolean
}

export default function CustomHeader({ 
  title = '管理后台',
  subtitle,
  onMenuToggle,
  showSearch = true,
  showActions = true
}: CustomHeaderProps) {
  const [isDark, setIsDark] = React.useState(false)
  const [notifications, setNotifications] = React.useState(3)

  const toggleTheme = () => {
    setIsDark(!isDark)
    // 这里应该实现主题切换逻辑
    document.documentElement.setAttribute('data-theme', isDark ? 'light' : 'dark')
  }

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* 左侧：菜单按钮和标题 */}
        <div className="flex items-center space-x-4">
          {onMenuToggle && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuToggle}
              className="lg:hidden"
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}
          
          <div>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {subtitle}
              </p>
            )}
          </div>
        </div>

        {/* 中间：搜索框 */}
        {showSearch && (
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户、产品、订单..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* 右侧：操作按钮 */}
        <div className="flex items-center space-x-2">
          {showActions && (
            <>
              {/* 快捷操作按钮 */}
              <div className="hidden lg:flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  新建
                </Button>
                
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
                
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </div>

              {/* 分隔线 */}
              <div className="hidden lg:block w-px h-6 bg-gray-300 dark:bg-gray-600" />
            </>
          )}

          {/* 刷新按钮 */}
          <Button variant="ghost" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>

          {/* 语言切换 */}
          <Button variant="ghost" size="icon">
            <Globe className="h-4 w-4" />
          </Button>

          {/* 主题切换 */}
          <Button variant="ghost" size="icon" onClick={toggleTheme}>
            {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>

          {/* 通知 */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-4 w-4" />
            {notifications > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {notifications > 9 ? '9+' : notifications}
              </Badge>
            )}
          </Button>

          {/* 设置 */}
          <Button variant="ghost" size="icon">
            <Settings className="h-4 w-4" />
          </Button>

          {/* 用户菜单 */}
          <div className="relative">
            <Button variant="ghost" size="icon">
              <User className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 移动端搜索框 */}
      {showSearch && (
        <div className="md:hidden mt-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* 快捷操作栏（移动端） */}
      {showActions && (
        <div className="lg:hidden mt-3 flex items-center space-x-2 overflow-x-auto">
          <Button variant="outline" size="sm" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            新建
          </Button>
          
          <Button variant="outline" size="sm" className="whitespace-nowrap">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          
          <Button variant="outline" size="sm" className="whitespace-nowrap">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          
          <Button variant="outline" size="sm" className="whitespace-nowrap">
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
        </div>
      )}

      {/* 面包屑导航 */}
      <div className="mt-3 flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
        <span>首页</span>
        <span>/</span>
        <span>管理后台</span>
        {subtitle && (
          <>
            <span>/</span>
            <span className="text-gray-900 dark:text-gray-100">{subtitle}</span>
          </>
        )}
      </div>
    </header>
  )
}
