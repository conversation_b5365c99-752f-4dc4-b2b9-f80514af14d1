import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../payload.config'

// 获取公司列表
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const verified = searchParams.get('verified') || ''
    const industry = searchParams.get('industry') || ''
    const size = searchParams.get('size') || ''
    const sortField = searchParams.get('sortField') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.or = [
        { name: { contains: search } },
        { description: { contains: search } },
        { website: { contains: search } },
        { contactEmail: { contains: search } },
        { contactPhone: { contains: search } }
      ]
    }
    
    if (verified !== '') {
      where.verified = { equals: verified === 'true' }
    }
    
    if (industry) {
      where.industry = { equals: industry }
    }
    
    if (size) {
      where.size = { equals: size }
    }

    // 查询公司
    const companies = await payload.find({
      collection: 'companies',
      where,
      page,
      limit,
      sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
      depth: 2,
    })

    // 统计数据
    const totalCompanies = await payload.count({
      collection: 'companies',
      where: {},
    })

    const verifiedCompanies = await payload.count({
      collection: 'companies',
      where: { verified: { equals: true } },
    })

    const pendingCompanies = await payload.count({
      collection: 'companies',
      where: { verified: { equals: false } },
    })

    // 按行业统计
    const industryStats = await payload.find({
      collection: 'companies',
      where: {},
      limit: 0,
    })

    const industryCount: { [key: string]: number } = {}
    industryStats.docs.forEach((company: any) => {
      const industry = company.industry || '未分类'
      industryCount[industry] = (industryCount[industry] || 0) + 1
    })

    return NextResponse.json({
      success: true,
      data: {
        companies: companies.docs,
        pagination: {
          page: companies.page,
          limit: companies.limit,
          totalPages: companies.totalPages,
          totalDocs: companies.totalDocs,
          hasNextPage: companies.hasNextPage,
          hasPrevPage: companies.hasPrevPage,
        },
        stats: {
          total: totalCompanies.totalDocs,
          verified: verifiedCompanies.totalDocs,
          pending: pendingCompanies.totalDocs,
          industryStats: industryCount,
        },
      },
    })
  } catch (error) {
    console.error('获取公司列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取公司列表失败' },
      { status: 500 }
    )
  }
}

// 创建新公司
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // 验证必填字段
    if (!body.name || !body.contactEmail) {
      return NextResponse.json(
        { success: false, error: '公司名称和联系邮箱为必填项' },
        { status: 400 }
      )
    }

    // 检查公司名称是否已存在
    const existingCompany = await payload.find({
      collection: 'companies',
      where: { name: { equals: body.name } },
      limit: 1,
    })

    if (existingCompany.docs.length > 0) {
      return NextResponse.json(
        { success: false, error: '该公司名称已存在' },
        { status: 400 }
      )
    }

    // 创建公司
    const company = await payload.create({
      collection: 'companies',
      data: {
        name: body.name,
        description: body.description || '',
        logo: body.logo || null,
        website: body.website || '',
        industry: body.industry || '',
        size: body.size || 'small',
        foundedYear: body.foundedYear || null,
        address: body.address || {
          street: '',
          city: '',
          state: '',
          country: '',
          zipCode: '',
        },
        contactEmail: body.contactEmail,
        contactPhone: body.contactPhone || '',
        contactPerson: body.contactPerson || '',
        verified: body.verified || false,
        verificationDocuments: body.verificationDocuments || [],
        businessLicense: body.businessLicense || '',
        taxId: body.taxId || '',
        socialMedia: body.socialMedia || {
          linkedin: '',
          twitter: '',
          facebook: '',
          instagram: '',
        },
        tags: body.tags || [],
        notes: body.notes || '',
        status: body.status || 'active',
      },
    })

    return NextResponse.json({
      success: true,
      data: company,
      message: '公司创建成功',
    })
  } catch (error) {
    console.error('创建公司失败:', error)
    return NextResponse.json(
      { success: false, error: '创建公司失败' },
      { status: 500 }
    )
  }
}

// 批量操作公司
export async function PATCH(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { action, companyIds, data } = body

    if (!action || !companyIds || !Array.isArray(companyIds)) {
      return NextResponse.json(
        { success: false, error: '参数错误' },
        { status: 400 }
      )
    }

    const results = []

    for (const companyId of companyIds) {
      try {
        let result
        
        switch (action) {
          case 'verify':
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { 
                verified: true,
                verifiedAt: new Date(),
              },
            })
            break
            
          case 'unverify':
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { 
                verified: false,
                verifiedAt: null,
              },
            })
            break
            
          case 'activate':
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { status: 'active' },
            })
            break
            
          case 'deactivate':
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { status: 'inactive' },
            })
            break
            
          case 'delete':
            result = await payload.delete({
              collection: 'companies',
              id: companyId,
            })
            break
            
          case 'updateIndustry':
            if (!data?.industry) {
              throw new Error('行业不能为空')
            }
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { industry: data.industry },
            })
            break
            
          case 'updateSize':
            if (!data?.size) {
              throw new Error('公司规模不能为空')
            }
            result = await payload.update({
              collection: 'companies',
              id: companyId,
              data: { size: data.size },
            })
            break
            
          default:
            throw new Error(`不支持的操作: ${action}`)
        }
        
        results.push({ companyId, success: true, data: result })
      } catch (error) {
        results.push({ 
          companyId, 
          success: false, 
          error: error instanceof Error ? error.message : '操作失败' 
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: results,
      message: `操作完成: ${successCount} 成功, ${failCount} 失败`,
    })
  } catch (error) {
    console.error('批量操作失败:', error)
    return NextResponse.json(
      { success: false, error: '批量操作失败' },
      { status: 500 }
    )
  }
}

// 导出公司数据
export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { format = 'csv', filters = {} } = body

    // 构建查询条件
    const where: any = {}
    
    if (filters.verified !== undefined) {
      where.verified = { equals: filters.verified }
    }
    
    if (filters.industry) {
      where.industry = { equals: filters.industry }
    }
    
    if (filters.size) {
      where.size = { equals: filters.size }
    }

    // 获取所有符合条件的公司
    const companies = await payload.find({
      collection: 'companies',
      where,
      limit: 10000, // 设置一个合理的上限
      depth: 1,
    })

    // 根据格式处理数据
    let exportData
    let contentType
    let filename

    if (format === 'csv') {
      // 生成CSV格式
      const headers = [
        '公司名称', '描述', '网站', '行业', '规模', '成立年份',
        '联系邮箱', '联系电话', '联系人', '是否认证', '状态', '创建时间'
      ]
      
      const rows = companies.docs.map((company: any) => [
        company.name || '',
        company.description || '',
        company.website || '',
        company.industry || '',
        company.size || '',
        company.foundedYear || '',
        company.contactEmail || '',
        company.contactPhone || '',
        company.contactPerson || '',
        company.verified ? '是' : '否',
        company.status || '',
        new Date(company.createdAt).toLocaleDateString(),
      ])

      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')

      exportData = csvContent
      contentType = 'text/csv'
      filename = `companies_${new Date().toISOString().split('T')[0]}.csv`
    } else {
      // JSON格式
      exportData = JSON.stringify(companies.docs, null, 2)
      contentType = 'application/json'
      filename = `companies_${new Date().toISOString().split('T')[0]}.json`
    }

    return new NextResponse(exportData, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })
  } catch (error) {
    console.error('导出公司数据失败:', error)
    return NextResponse.json(
      { success: false, error: '导出数据失败' },
      { status: 500 }
    )
  }
}
