import type { GlobalConfig } from 'payload'
import { admins } from '../access/index'

export const Navigation: GlobalConfig = {
  slug: 'navigation',
  label: '导航管理',
  access: {
    read: () => true,
    update: admins,
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: '主导航',
          fields: [
            {
              name: 'mainNavigation',
              label: '主导航菜单',
              type: 'array',
              fields: [
                {
                  name: 'label',
                  label: '菜单名称',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'type',
                  label: '链接类型',
                  type: 'select',
                  required: true,
                  defaultValue: 'internal',
                  options: [
                    { label: '内部链接', value: 'internal' },
                    { label: '外部链接', value: 'external' },
                    { label: '下拉菜单', value: 'dropdown' },
                  ],
                },
                {
                  name: 'url',
                  label: '链接地址',
                  type: 'text',
                  admin: {
                    condition: (data, siblingData) => siblingData?.type !== 'dropdown',
                  },
                },
                {
                  name: 'openInNewTab',
                  label: '新窗口打开',
                  type: 'checkbox',
                  defaultValue: false,
                  admin: {
                    condition: (data, siblingData) => siblingData?.type === 'external',
                  },
                },
                {
                  name: 'icon',
                  label: '图标',
                  type: 'text',
                  admin: {
                    description: '图标类名或SVG代码',
                  },
                },
                {
                  name: 'badge',
                  label: '徽章',
                  type: 'group',
                  fields: [
                    {
                      name: 'text',
                      label: '徽章文本',
                      type: 'text',
                    },
                    {
                      name: 'color',
                      label: '徽章颜色',
                      type: 'select',
                      options: [
                        { label: '红色', value: 'red' },
                        { label: '蓝色', value: 'blue' },
                        { label: '绿色', value: 'green' },
                        { label: '黄色', value: 'yellow' },
                        { label: '紫色', value: 'purple' },
                      ],
                    },
                  ],
                },
                {
                  name: 'submenu',
                  label: '子菜单',
                  type: 'array',
                  admin: {
                    condition: (data, siblingData) => siblingData?.type === 'dropdown',
                  },
                  fields: [
                    {
                      name: 'label',
                      label: '子菜单名称',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'url',
                      label: '链接地址',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'description',
                      label: '描述',
                      type: 'text',
                    },
                    {
                      name: 'icon',
                      label: '图标',
                      type: 'text',
                    },
                    {
                      name: 'featured',
                      label: '推荐项目',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                  ],
                },
                {
                  name: 'accessLevel',
                  label: '访问权限',
                  type: 'select',
                  defaultValue: 'public',
                  options: [
                    { label: '公开', value: 'public' },
                    { label: '登录用户', value: 'user' },
                    { label: 'VIP用户', value: 'vip' },
                    { label: '管理员', value: 'admin' },
                  ],
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
                {
                  name: 'enabled',
                  label: '启用',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
        {
          label: '底部导航',
          fields: [
            {
              name: 'footerNavigation',
              label: '底部导航',
              type: 'array',
              fields: [
                {
                  name: 'title',
                  label: '分组标题',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'links',
                  label: '链接列表',
                  type: 'array',
                  fields: [
                    {
                      name: 'label',
                      label: '链接名称',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'url',
                      label: '链接地址',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'openInNewTab',
                      label: '新窗口打开',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                  ],
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
              ],
            },
          ],
        },
        {
          label: '移动端导航',
          fields: [
            {
              name: 'mobileNavigation',
              label: '移动端底部导航',
              type: 'array',
              maxRows: 5,
              fields: [
                {
                  name: 'label',
                  label: '标签',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'url',
                  label: '链接地址',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'icon',
                  label: '图标',
                  type: 'text',
                  required: true,
                  admin: {
                    description: '图标类名或SVG代码',
                  },
                },
                {
                  name: 'activeIcon',
                  label: '激活状态图标',
                  type: 'text',
                  admin: {
                    description: '选中时的图标，留空则使用默认图标',
                  },
                },
                {
                  name: 'badge',
                  label: '徽章数字',
                  type: 'number',
                  min: 0,
                  admin: {
                    description: '显示在图标上的数字徽章',
                  },
                },
                {
                  name: 'accessLevel',
                  label: '访问权限',
                  type: 'select',
                  defaultValue: 'public',
                  options: [
                    { label: '公开', value: 'public' },
                    { label: '登录用户', value: 'user' },
                    { label: 'VIP用户', value: 'vip' },
                  ],
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
                {
                  name: 'enabled',
                  label: '启用',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
        {
          label: '面包屑导航',
          fields: [
            {
              name: 'breadcrumbSettings',
              label: '面包屑设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用面包屑',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'showHome',
                  label: '显示首页',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'homeLabel',
                  label: '首页标签',
                  type: 'text',
                  defaultValue: '首页',
                  admin: {
                    condition: (data, siblingData) => siblingData?.showHome,
                  },
                },
                {
                  name: 'separator',
                  label: '分隔符',
                  type: 'text',
                  defaultValue: '>',
                },
                {
                  name: 'maxItems',
                  label: '最大显示项目',
                  type: 'number',
                  defaultValue: 5,
                  min: 2,
                },
              ],
            },
          ],
        },
        {
          label: '快捷导航',
          fields: [
            {
              name: 'quickNavigation',
              label: '快捷导航',
              type: 'array',
              admin: {
                description: '显示在页面顶部或侧边的快捷链接',
              },
              fields: [
                {
                  name: 'label',
                  label: '标签',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'url',
                  label: '链接地址',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'icon',
                  label: '图标',
                  type: 'text',
                },
                {
                  name: 'color',
                  label: '颜色',
                  type: 'select',
                  options: [
                    { label: '默认', value: 'default' },
                    { label: '主色调', value: 'primary' },
                    { label: '成功', value: 'success' },
                    { label: '警告', value: 'warning' },
                    { label: '危险', value: 'danger' },
                  ],
                },
                {
                  name: 'position',
                  label: '显示位置',
                  type: 'select',
                  defaultValue: 'header',
                  options: [
                    { label: '顶部', value: 'header' },
                    { label: '侧边栏', value: 'sidebar' },
                    { label: '浮动按钮', value: 'floating' },
                  ],
                },
                {
                  name: 'accessLevel',
                  label: '访问权限',
                  type: 'select',
                  defaultValue: 'public',
                  options: [
                    { label: '公开', value: 'public' },
                    { label: '登录用户', value: 'user' },
                    { label: 'VIP用户', value: 'vip' },
                    { label: '管理员', value: 'admin' },
                  ],
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
                {
                  name: 'enabled',
                  label: '启用',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
