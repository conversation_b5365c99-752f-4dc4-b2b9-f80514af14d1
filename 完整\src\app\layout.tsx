import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'
import '@/styles/admin.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'B2B贸易平台 - 全球领先的企业级贸易解决方案',
  description: '专业的B2B贸易信息平台，连接全球供应商与采购商，提供用户管理、产品管理、订单处理、支付系统等完整功能。',
  keywords: 'B2B, 贸易平台, 供应商, 采购商, 企业管理, 订单管理, 支付系统',
  authors: [{ name: 'B2B贸易平台开发团队' }],
  creator: 'B2B贸易平台',
  publisher: 'B2B贸易平台',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'zh-CN': '/zh-CN',
      'en-US': '/en-US',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: 'B2B贸易平台 - 全球领先的企业级贸易解决方案',
    description: '专业的B2B贸易信息平台，连接全球供应商与采购商，提供完整的企业级贸易解决方案。',
    siteName: 'B2B贸易平台',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'B2B贸易平台',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'B2B贸易平台 - 全球领先的企业级贸易解决方案',
    description: '专业的B2B贸易信息平台，连接全球供应商与采购商。',
    images: ['/og-image.jpg'],
    creator: '@b2b_platform',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#3b82f6" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        <meta name="theme-color" content="#ffffff" />
        
        {/* 预加载关键资源 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS预解析 */}
        <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
        <link rel="dns-prefetch" href="//unpkg.com" />
        
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'B2B贸易平台',
              url: process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000',
              logo: `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/logo.png`,
              description: '专业的B2B贸易信息平台，连接全球供应商与采购商',
              contactPoint: {
                '@type': 'ContactPoint',
                telephone: '+86-************',
                contactType: 'customer service',
                availableLanguage: ['Chinese', 'English'],
              },
              sameAs: [
                'https://www.facebook.com/b2bplatform',
                'https://www.twitter.com/b2b_platform',
                'https://www.linkedin.com/company/b2b-platform',
              ],
            }),
          }}
        />
      </head>
      <body className={inter.className}>
        {/* 页面加载指示器 */}
        <div id="page-loading" className="fixed inset-0 z-50 bg-white flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>

        {/* 主要内容 */}
        <main id="main-content">
          {children}
        </main>

        {/* 返回顶部按钮 */}
        <button
          id="back-to-top"
          className="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible z-40"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          aria-label="返回顶部"
        >
          <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </button>

        {/* 客服聊天按钮 */}
        <div className="fixed bottom-8 left-8 z-40">
          <button className="w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-all duration-300 flex items-center justify-center">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </button>
        </div>

        {/* 全局脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 页面加载完成后隐藏加载指示器
              window.addEventListener('load', function() {
                const loading = document.getElementById('page-loading');
                if (loading) {
                  loading.style.opacity = '0';
                  setTimeout(() => {
                    loading.style.display = 'none';
                  }, 300);
                }
              });

              // 返回顶部按钮显示/隐藏
              window.addEventListener('scroll', function() {
                const backToTop = document.getElementById('back-to-top');
                if (window.scrollY > 300) {
                  backToTop.classList.remove('opacity-0', 'invisible');
                  backToTop.classList.add('opacity-100', 'visible');
                } else {
                  backToTop.classList.add('opacity-0', 'invisible');
                  backToTop.classList.remove('opacity-100', 'visible');
                }
              });

              // 平滑滚动到锚点
              document.addEventListener('click', function(e) {
                if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
                  e.preventDefault();
                  const target = document.querySelector(e.target.getAttribute('href'));
                  if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                  }
                }
              });

              // 主题检测
              if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
                document.documentElement.setAttribute('data-theme', 'dark');
              } else {
                document.documentElement.classList.remove('dark');
                document.documentElement.setAttribute('data-theme', 'light');
              }

              // 性能监控
              if ('performance' in window) {
                window.addEventListener('load', function() {
                  setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('页面加载时间:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                  }, 0);
                });
              }

              // 错误监控
              window.addEventListener('error', function(e) {
                console.error('JavaScript错误:', e.error);
                // 这里可以发送错误信息到监控服务
              });

              // 未处理的Promise拒绝
              window.addEventListener('unhandledrejection', function(e) {
                console.error('未处理的Promise拒绝:', e.reason);
                // 这里可以发送错误信息到监控服务
              });
            `,
          }}
        />

        {/* 百度统计或其他分析代码 */}
        {process.env.NODE_ENV === 'production' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // 百度统计代码
                var _hmt = _hmt || [];
                (function() {
                  var hm = document.createElement("script");
                  hm.src = "https://hm.baidu.com/hm.js?your-baidu-analytics-id";
                  var s = document.getElementsByTagName("script")[0]; 
                  s.parentNode.insertBefore(hm, s);
                })();
              `,
            }}
          />
        )}
      </body>
    </html>
  )
}
