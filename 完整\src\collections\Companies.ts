import type { CollectionConfig } from 'payload'
import { admins, adminsAndUser, anyone } from '../access/index'

export const Companies: CollectionConfig = {
  slug: 'companies',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'type', 'industry', 'verificationStatus', 'createdAt'],
    listSearchableFields: ['name', 'description', 'address'],
    group: '企业管理',
  },
  access: {
    read: anyone,
    create: adminsAndUser,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role?.includes('admin')) return true
      
      return {
        owner: {
          equals: user.id,
        },
      }
    },
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create' && req.user) {
          data.owner = req.user.id
          data.verificationStatus = 'pending'
          data.status = 'active'
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'name',
      label: '公司名称',
      type: 'text',
      required: true,
      admin: {
        placeholder: '请输入公司全称',
      },
    },
    {
      name: 'slug',
      label: 'URL别名',
      type: 'text',
      unique: true,
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.name) {
              return data.name
                .toLowerCase()
                .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'owner',
      label: '所有者',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: '公司的主要负责人',
      },
    },
    {
      name: 'type',
      label: '企业类型',
      type: 'select',
      required: true,
      options: [
        { label: '制造商', value: 'manufacturer' },
        { label: '贸易商', value: 'trader' },
        { label: '供应商', value: 'supplier' },
        { label: '分销商', value: 'distributor' },
        { label: '服务商', value: 'service_provider' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'industry',
      label: '所属行业',
      type: 'select',
      required: true,
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '机械设备', value: 'machinery' },
        { label: '化工原料', value: 'chemicals' },
        { label: '纺织服装', value: 'textiles' },
        { label: '建筑材料', value: 'construction' },
        { label: '食品饮料', value: 'food' },
        { label: '汽车配件', value: 'automotive' },
        { label: '医疗器械', value: 'medical' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'description',
      label: '公司简介',
      type: 'richText',
      admin: {
        description: '详细的公司介绍',
      },
    },
    {
      name: 'logo',
      label: '公司Logo',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'banner',
      label: '公司横幅',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'contactInfo',
      label: '联系信息',
      type: 'group',
      fields: [
        {
          name: 'phone',
          label: '联系电话',
          type: 'text',
        },
        {
          name: 'fax',
          label: '传真',
          type: 'text',
        },
        {
          name: 'email',
          label: '邮箱',
          type: 'email',
        },
        {
          name: 'website',
          label: '官网',
          type: 'text',
        },
        {
          name: 'address',
          label: '地址',
          type: 'textarea',
        },
        {
          name: 'zipCode',
          label: '邮编',
          type: 'text',
        },
        {
          name: 'country',
          label: '国家',
          type: 'text',
          defaultValue: '中国',
        },
        {
          name: 'province',
          label: '省份',
          type: 'text',
        },
        {
          name: 'city',
          label: '城市',
          type: 'text',
        },
      ],
    },
    {
      name: 'businessInfo',
      label: '营业信息',
      type: 'group',
      fields: [
        {
          name: 'registrationNumber',
          label: '工商注册号',
          type: 'text',
        },
        {
          name: 'taxId',
          label: '税务登记号',
          type: 'text',
        },
        {
          name: 'legalRepresentative',
          label: '法定代表人',
          type: 'text',
        },
        {
          name: 'registeredCapital',
          label: '注册资本',
          type: 'text',
        },
        {
          name: 'establishedDate',
          label: '成立日期',
          type: 'date',
        },
        {
          name: 'businessScope',
          label: '经营范围',
          type: 'textarea',
        },
      ],
    },
    {
      name: 'certificates',
      label: '资质证书',
      type: 'array',
      fields: [
        {
          name: 'name',
          label: '证书名称',
          type: 'text',
          required: true,
        },
        {
          name: 'number',
          label: '证书编号',
          type: 'text',
        },
        {
          name: 'issuer',
          label: '颁发机构',
          type: 'text',
        },
        {
          name: 'issueDate',
          label: '颁发日期',
          type: 'date',
        },
        {
          name: 'expiryDate',
          label: '有效期至',
          type: 'date',
        },
        {
          name: 'file',
          label: '证书文件',
          type: 'upload',
          relationTo: 'media',
        },
      ],
    },
    {
      name: 'verificationStatus',
      label: '认证状态',
      type: 'select',
      defaultValue: 'pending',
      options: [
        { label: '待审核', value: 'pending' },
        { label: '已认证', value: 'verified' },
        { label: '被拒绝', value: 'rejected' },
        { label: '已过期', value: 'expired' },
      ],
      access: {
        update: admins,
      },
    },
    {
      name: 'verificationNotes',
      label: '认证备注',
      type: 'textarea',
      admin: {
        description: '审核意见或备注',
      },
      access: {
        update: admins,
      },
    },
    {
      name: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'active',
      options: [
        { label: '正常', value: 'active' },
        { label: '暂停', value: 'suspended' },
        { label: '禁用', value: 'disabled' },
      ],
      access: {
        update: admins,
      },
    },
    {
      name: 'rating',
      label: '信用评级',
      type: 'select',
      options: [
        { label: 'AAA', value: 'AAA' },
        { label: 'AA', value: 'AA' },
        { label: 'A', value: 'A' },
        { label: 'BBB', value: 'BBB' },
        { label: 'BB', value: 'BB' },
        { label: 'B', value: 'B' },
        { label: 'CCC', value: 'CCC' },
        { label: 'CC', value: 'CC' },
        { label: 'C', value: 'C' },
      ],
      access: {
        update: admins,
      },
    },
    {
      name: 'socialMedia',
      label: '社交媒体',
      type: 'group',
      fields: [
        {
          name: 'wechat',
          label: '微信',
          type: 'text',
        },
        {
          name: 'weibo',
          label: '微博',
          type: 'text',
        },
        {
          name: 'linkedin',
          label: 'LinkedIn',
          type: 'text',
        },
        {
          name: 'facebook',
          label: 'Facebook',
          type: 'text',
        },
        {
          name: 'twitter',
          label: 'Twitter',
          type: 'text',
        },
      ],
    },
    {
      name: 'statistics',
      label: '统计数据',
      type: 'group',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'productCount',
          label: '产品数量',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'orderCount',
          label: '订单数量',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'viewCount',
          label: '浏览次数',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'inquiryCount',
          label: '询价次数',
          type: 'number',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'seo',
      label: 'SEO设置',
      type: 'group',
      fields: [
        {
          name: 'title',
          label: 'SEO标题',
          type: 'text',
          maxLength: 60,
        },
        {
          name: 'description',
          label: 'SEO描述',
          type: 'textarea',
          maxLength: 160,
        },
        {
          name: 'keywords',
          label: '关键词',
          type: 'text',
          hasMany: true,
        },
      ],
    },
  ],
  timestamps: true,
}
