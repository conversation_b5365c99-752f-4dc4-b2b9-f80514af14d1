import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../payload.config'

// 获取用户列表
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''
    const vipLevel = searchParams.get('vipLevel') || ''
    const status = searchParams.get('status') || ''
    const sortField = searchParams.get('sortField') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.or = [
        { name: { contains: search } },
        { email: { contains: search } },
        { phone: { contains: search } },
        { company: { contains: search } }
      ]
    }
    
    if (role) {
      where.role = { equals: role }
    }
    
    if (vipLevel) {
      where.vipLevel = { equals: vipLevel }
    }
    
    if (status) {
      where.status = { equals: status }
    }

    // 查询用户
    const users = await payload.find({
      collection: 'users',
      where,
      page,
      limit,
      sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
      depth: 1,
    })

    // 统计数据
    const totalUsers = await payload.count({
      collection: 'users',
      where: {},
    })

    const activeUsers = await payload.count({
      collection: 'users',
      where: { status: { equals: 'active' } },
    })

    const vipUsers = await payload.count({
      collection: 'users',
      where: { vipLevel: { not_equals: 'basic' } },
    })

    const newUsersThisMonth = await payload.count({
      collection: 'users',
      where: {
        createdAt: {
          greater_than: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        users: users.docs,
        pagination: {
          page: users.page,
          limit: users.limit,
          totalPages: users.totalPages,
          totalDocs: users.totalDocs,
          hasNextPage: users.hasNextPage,
          hasPrevPage: users.hasPrevPage,
        },
        stats: {
          total: totalUsers.totalDocs,
          active: activeUsers.totalDocs,
          vip: vipUsers.totalDocs,
          newThisMonth: newUsersThisMonth.totalDocs,
        },
      },
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取用户列表失败' },
      { status: 500 }
    )
  }
}

// 创建新用户
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // 验证必填字段
    if (!body.email || !body.password) {
      return NextResponse.json(
        { success: false, error: '邮箱和密码为必填项' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    const existingUser = await payload.find({
      collection: 'users',
      where: { email: { equals: body.email } },
      limit: 1,
    })

    if (existingUser.docs.length > 0) {
      return NextResponse.json(
        { success: false, error: '该邮箱已被注册' },
        { status: 400 }
      )
    }

    // 创建用户
    const user = await payload.create({
      collection: 'users',
      data: {
        email: body.email,
        password: body.password,
        name: body.name || '',
        phone: body.phone || '',
        company: body.company || '',
        role: body.role || 'user',
        vipLevel: body.vipLevel || 'basic',
        status: body.status || 'active',
        points: body.points || 0,
        avatar: body.avatar || null,
        address: body.address || '',
        website: body.website || '',
        description: body.description || '',
        preferences: {
          language: body.language || 'zh-CN',
          currency: body.currency || 'CNY',
          timezone: body.timezone || 'Asia/Shanghai',
          notifications: {
            email: body.emailNotifications !== false,
            sms: body.smsNotifications !== false,
            push: body.pushNotifications !== false,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: user,
      message: '用户创建成功',
    })
  } catch (error) {
    console.error('创建用户失败:', error)
    return NextResponse.json(
      { success: false, error: '创建用户失败' },
      { status: 500 }
    )
  }
}

// 批量操作用户
export async function PATCH(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { action, userIds, data } = body

    if (!action || !userIds || !Array.isArray(userIds)) {
      return NextResponse.json(
        { success: false, error: '参数错误' },
        { status: 400 }
      )
    }

    const results = []

    for (const userId of userIds) {
      try {
        let result
        
        switch (action) {
          case 'activate':
            result = await payload.update({
              collection: 'users',
              id: userId,
              data: { status: 'active' },
            })
            break
            
          case 'deactivate':
            result = await payload.update({
              collection: 'users',
              id: userId,
              data: { status: 'inactive' },
            })
            break
            
          case 'delete':
            result = await payload.delete({
              collection: 'users',
              id: userId,
            })
            break
            
          case 'updateVip':
            if (!data?.vipLevel) {
              throw new Error('VIP等级不能为空')
            }
            result = await payload.update({
              collection: 'users',
              id: userId,
              data: { vipLevel: data.vipLevel },
            })
            break
            
          case 'updateRole':
            if (!data?.role) {
              throw new Error('角色不能为空')
            }
            result = await payload.update({
              collection: 'users',
              id: userId,
              data: { role: data.role },
            })
            break
            
          case 'addPoints':
            if (!data?.points || isNaN(data.points)) {
              throw new Error('积分数量无效')
            }
            const user = await payload.findByID({
              collection: 'users',
              id: userId,
            })
            result = await payload.update({
              collection: 'users',
              id: userId,
              data: { points: (user.points || 0) + parseInt(data.points) },
            })
            break
            
          default:
            throw new Error(`不支持的操作: ${action}`)
        }
        
        results.push({ userId, success: true, data: result })
      } catch (error) {
        results.push({ 
          userId, 
          success: false, 
          error: error instanceof Error ? error.message : '操作失败' 
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: results,
      message: `操作完成: ${successCount} 成功, ${failCount} 失败`,
    })
  } catch (error) {
    console.error('批量操作失败:', error)
    return NextResponse.json(
      { success: false, error: '批量操作失败' },
      { status: 500 }
    )
  }
}
