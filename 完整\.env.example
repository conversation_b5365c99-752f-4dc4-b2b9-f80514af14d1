# =============================================================================
# B2B贸易平台环境变量配置文件
# 复制此文件为 .env.local 并填入实际值
# =============================================================================

# 应用基础配置
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_SERVER_URL=https://yourdomain.com
PAYLOAD_PUBLIC_SERVER_URL=https://yourdomain.com
PRODUCTION_URL=https://yourdomain.com

# Payload CMS 配置
PAYLOAD_SECRET=your-super-secret-key-change-in-production-minimum-32-characters
PAYLOAD_CONFIG_PATH=payload.config.ts

# 数据库配置 (MongoDB)
DATABASE_URI=mongodb://localhost:27017/b2b-trade-platform
# 或者使用 MongoDB Atlas
# DATABASE_URI=mongodb+srv://username:<EMAIL>/b2b-trade-platform

# JWT 认证配置
JWT_SECRET=your-jwt-secret-key-minimum-32-characters-long
JWT_EXPIRES_IN=7d

# 文件上传配置 (UploadThing)
UPLOADTHING_TOKEN=your-uploadthing-token
UPLOADTHING_APP_ID=your-uploadthing-app-id

# 支付配置 (PayPal)
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_BUSINESS_EMAIL=<EMAIL>
PAYPAL_ENVIRONMENT=sandbox
# 生产环境改为: PAYPAL_ENVIRONMENT=live

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=B2B贸易平台

# 短信服务配置 (阿里云)
ALIYUN_ACCESS_KEY_ID=your-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_SMS_SIGN_NAME=your-sms-sign
ALIYUN_SMS_TEMPLATE_CODE=SMS_123456789

# 微信支付配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_MCH_ID=your-merchant-id
WECHAT_API_KEY=your-api-key
WECHAT_CERT_PATH=/path/to/cert.pem
WECHAT_KEY_PATH=/path/to/key.pem

# 支付宝配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-private-key
ALIPAY_PUBLIC_KEY=alipay-public-key
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do
# 沙箱环境: https://openapi.alipaydev.com/gateway.do

# Redis 配置 (可选，用于缓存和会话)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/b2b-platform

# 安全配置
RATE_LIMIT_MAX=2000
RATE_LIMIT_WINDOW_MS=900000
CORS_ORIGIN=https://yourdomain.com

# 第三方服务配置
# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# 百度统计
NEXT_PUBLIC_BAIDU_ANALYTICS_ID=your-baidu-id

# 腾讯云COS (对象存储)
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
TENCENT_BUCKET=your-bucket-name
TENCENT_REGION=ap-shanghai

# 阿里云OSS (对象存储)
ALIYUN_OSS_ACCESS_KEY_ID=your-access-key-id
ALIYUN_OSS_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_BUCKET=your-bucket-name
ALIYUN_OSS_REGION=oss-cn-shanghai
ALIYUN_OSS_ENDPOINT=https://oss-cn-shanghai.aliyuncs.com

# 宝塔面板特定配置
# 如果使用宝塔的 PM2 管理，可以设置这些变量
PM2_APP_NAME=b2b-trade-platform
PM2_INSTANCES=max
PM2_EXEC_MODE=cluster

# 数据库备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/www/backups/b2b-platform

# 监控和错误追踪
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=production

# 开发环境特定配置
NEXT_TELEMETRY_DISABLED=1
DISABLE_LOGGING=false

# =============================================================================
# 宝塔部署说明:
# 1. 将此文件复制为 .env.local
# 2. 填入实际的配置值
# 3. 确保文件权限设置为 600 (仅所有者可读写)
# 4. 在宝塔面板中设置 Node.js 项目的环境变量
# =============================================================================
