import type { CollectionConfig } from 'payload'
import { admins } from '../access/index'

export const AccessLogs: CollectionConfig = {
  slug: 'access-logs',
  admin: {
    useAsTitle: 'action',
    defaultColumns: ['user', 'action', 'ip', 'success', 'timestamp'],
    listSearchableFields: ['action', 'ip', 'userAgent'],
    group: '系统管理',
    pagination: {
      defaultLimit: 50,
    },
  },
  access: {
    read: admins,
    create: admins,
    update: admins,
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 自动设置时间戳
          if (!data.timestamp) {
            data.timestamp = new Date().toISOString()
          }
          
          // 自动获取IP地址
          if (!data.ip && req.ip) {
            data.ip = req.ip
          }
          
          // 自动获取用户代理
          if (!data.userAgent && req.get) {
            data.userAgent = req.get('User-Agent')
          }
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'user',
      label: '用户',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: '执行操作的用户，可为空（匿名访问）',
      },
    },
    {
      name: 'action',
      label: '操作类型',
      type: 'select',
      required: true,
      options: [
        // 认证相关
        { label: '登录', value: 'login' },
        { label: '登出', value: 'logout' },
        { label: '注册', value: 'register' },
        { label: '注册并登录', value: 'register_and_login' },
        { label: '密码重置', value: 'password_reset' },
        { label: '邮箱验证', value: 'email_verify' },
        
        // 页面访问
        { label: '首页访问', value: 'home_visit' },
        { label: '产品列表', value: 'product_list' },
        { label: '产品详情', value: 'product_detail' },
        { label: '公司详情', value: 'company_detail' },
        { label: '搜索', value: 'search' },
        
        // 用户操作
        { label: '资料更新', value: 'profile_update' },
        { label: '密码修改', value: 'password_change' },
        { label: '头像上传', value: 'avatar_upload' },
        
        // 产品操作
        { label: '产品创建', value: 'product_create' },
        { label: '产品更新', value: 'product_update' },
        { label: '产品删除', value: 'product_delete' },
        { label: '产品收藏', value: 'product_favorite' },
        
        // 订单操作
        { label: '订单创建', value: 'order_create' },
        { label: '订单更新', value: 'order_update' },
        { label: '订单取消', value: 'order_cancel' },
        
        // 支付操作
        { label: '支付发起', value: 'payment_initiate' },
        { label: '支付成功', value: 'payment_success' },
        { label: '支付失败', value: 'payment_failed' },
        
        // 文件操作
        { label: '文件上传', value: 'file_upload' },
        { label: '文件下载', value: 'file_download' },
        
        // 管理员操作
        { label: '管理员登录', value: 'admin_login' },
        { label: '用户管理', value: 'user_management' },
        { label: '系统设置', value: 'system_settings' },
        
        // API访问
        { label: 'API调用', value: 'api_call' },
        
        // 其他
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'resource',
      label: '访问资源',
      type: 'text',
      admin: {
        description: '访问的具体资源，如产品ID、页面路径等',
      },
    },
    {
      name: 'method',
      label: 'HTTP方法',
      type: 'select',
      options: [
        { label: 'GET', value: 'GET' },
        { label: 'POST', value: 'POST' },
        { label: 'PUT', value: 'PUT' },
        { label: 'DELETE', value: 'DELETE' },
        { label: 'PATCH', value: 'PATCH' },
      ],
    },
    {
      name: 'url',
      label: '访问URL',
      type: 'text',
    },
    {
      name: 'ip',
      label: 'IP地址',
      type: 'text',
      required: true,
    },
    {
      name: 'userAgent',
      label: '用户代理',
      type: 'text',
    },
    {
      name: 'referer',
      label: '来源页面',
      type: 'text',
    },
    {
      name: 'timestamp',
      label: '时间戳',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'success',
      label: '操作成功',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'statusCode',
      label: 'HTTP状态码',
      type: 'number',
      admin: {
        description: 'HTTP响应状态码',
      },
    },
    {
      name: 'responseTime',
      label: '响应时间(ms)',
      type: 'number',
      admin: {
        description: '请求处理时间，单位毫秒',
      },
    },
    {
      name: 'error',
      label: '错误信息',
      type: 'textarea',
      admin: {
        condition: (data) => !data.success,
      },
    },
    {
      name: 'sessionId',
      label: '会话ID',
      type: 'text',
    },
    {
      name: 'deviceInfo',
      label: '设备信息',
      type: 'group',
      fields: [
        {
          name: 'type',
          label: '设备类型',
          type: 'select',
          options: [
            { label: '桌面', value: 'desktop' },
            { label: '手机', value: 'mobile' },
            { label: '平板', value: 'tablet' },
            { label: '其他', value: 'other' },
          ],
        },
        {
          name: 'os',
          label: '操作系统',
          type: 'text',
        },
        {
          name: 'browser',
          label: '浏览器',
          type: 'text',
        },
        {
          name: 'version',
          label: '浏览器版本',
          type: 'text',
        },
      ],
    },
    {
      name: 'location',
      label: '地理位置',
      type: 'group',
      fields: [
        {
          name: 'country',
          label: '国家',
          type: 'text',
        },
        {
          name: 'region',
          label: '地区',
          type: 'text',
        },
        {
          name: 'city',
          label: '城市',
          type: 'text',
        },
        {
          name: 'latitude',
          label: '纬度',
          type: 'number',
        },
        {
          name: 'longitude',
          label: '经度',
          type: 'number',
        },
      ],
    },
    {
      name: 'requestData',
      label: '请求数据',
      type: 'json',
      admin: {
        description: '请求的参数或数据（敏感信息已过滤）',
      },
    },
    {
      name: 'responseData',
      label: '响应数据',
      type: 'json',
      admin: {
        description: '响应的数据摘要',
      },
    },
    {
      name: 'tags',
      label: '标签',
      type: 'text',
      hasMany: true,
      admin: {
        description: '用于分类和搜索的标签',
      },
    },
    {
      name: 'severity',
      label: '严重程度',
      type: 'select',
      defaultValue: 'info',
      options: [
        { label: '信息', value: 'info' },
        { label: '警告', value: 'warning' },
        { label: '错误', value: 'error' },
        { label: '严重', value: 'critical' },
      ],
    },
    {
      name: 'notes',
      label: '备注',
      type: 'textarea',
    },
  ],
  timestamps: false, // 使用自定义的timestamp字段
}
