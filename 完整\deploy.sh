#!/bin/bash

# =============================================================================
# B2B贸易平台宝塔部署脚本
# 使用方法: chmod +x deploy.sh && ./deploy.sh
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查宝塔面板是否安装
check_bt_panel() {
    if [ ! -f "/etc/init.d/bt" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    log_success "宝塔面板已安装"
}

# 安装Node.js (如果未安装)
install_nodejs() {
    log_info "检查Node.js安装状态..."
    
    if ! command -v node &> /dev/null; then
        log_info "安装Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        apt-get install -y nodejs
    fi
    
    NODE_VERSION=$(node --version)
    log_success "Node.js版本: $NODE_VERSION"
}

# 安装MongoDB (如果未安装)
install_mongodb() {
    log_info "检查MongoDB安装状态..."
    
    if ! command -v mongod &> /dev/null; then
        log_info "安装MongoDB..."
        wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -
        echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list
        apt-get update
        apt-get install -y mongodb-org
        systemctl start mongod
        systemctl enable mongod
    fi
    
    log_success "MongoDB已安装并启动"
}

# 安装PM2
install_pm2() {
    log_info "安装PM2..."
    npm install -g pm2
    pm2 startup
    log_success "PM2已安装"
}

# 创建项目目录
create_project_dir() {
    PROJECT_DIR="/www/wwwroot/b2b-trade-platform"
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_info "创建项目目录: $PROJECT_DIR"
        mkdir -p "$PROJECT_DIR"
        mkdir -p "$PROJECT_DIR/logs"
        mkdir -p "$PROJECT_DIR/uploads"
        mkdir -p "$PROJECT_DIR/backups"
    fi
    
    # 设置权限
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    log_success "项目目录已创建"
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."
    cd "$PROJECT_DIR"
    
    if [ -f "package.json" ]; then
        npm install --production
        log_success "依赖安装完成"
    else
        log_warning "未找到package.json文件"
    fi
}

# 构建项目
build_project() {
    log_info "构建项目..."
    cd "$PROJECT_DIR"
    
    if [ -f "package.json" ]; then
        npm run build
        log_success "项目构建完成"
    else
        log_warning "未找到package.json文件，跳过构建"
    fi
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    NGINX_CONF="/www/server/panel/vhost/nginx/b2b-trade-platform.conf"
    
    cat > "$NGINX_CONF" << 'EOF'
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL配置
    ssl_certificate /www/server/panel/vhost/cert/yourdomain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志
    access_log /www/wwwlogs/b2b-trade-platform.log;
    error_log /www/wwwlogs/b2b-trade-platform.error.log;
    
    # 静态文件
    location /_next/static/ {
        alias /www/wwwroot/b2b-trade-platform/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias /www/wwwroot/b2b-trade-platform/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # API路由
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 管理后台
    location /admin/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 主应用
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF
    
    log_success "Nginx配置已创建"
}

# 启动应用
start_application() {
    log_info "启动应用..."
    cd "$PROJECT_DIR"
    
    # 停止现有进程
    pm2 delete b2b-trade-platform 2>/dev/null || true
    
    # 启动新进程
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    log_success "应用已启动"
}

# 设置定时任务
setup_cron_jobs() {
    log_info "设置定时任务..."
    
    # 数据库备份
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/bin/mongodump --db b2b-trade-platform --out /www/backups/mongodb/\$(date +\%Y\%m\%d)") | crontab -
    
    # 日志清理
    (crontab -l 2>/dev/null; echo "0 3 * * 0 find /www/wwwroot/b2b-trade-platform/logs -name '*.log' -mtime +30 -delete") | crontab -
    
    log_success "定时任务已设置"
}

# 主函数
main() {
    log_info "开始部署B2B贸易平台..."
    
    check_root
    check_bt_panel
    install_nodejs
    install_mongodb
    install_pm2
    create_project_dir
    
    # 如果项目文件已存在，则安装依赖和构建
    if [ -f "/www/wwwroot/b2b-trade-platform/package.json" ]; then
        install_dependencies
        build_project
        start_application
    fi
    
    configure_nginx
    setup_cron_jobs
    
    log_success "部署完成！"
    log_info "请完成以下步骤："
    log_info "1. 上传项目文件到 /www/wwwroot/b2b-trade-platform"
    log_info "2. 复制 .env.example 为 .env.local 并配置环境变量"
    log_info "3. 在宝塔面板中配置SSL证书"
    log_info "4. 重启Nginx服务"
    log_info "5. 运行: cd /www/wwwroot/b2b-trade-platform && npm install && npm run build && pm2 restart b2b-trade-platform"
}

# 执行主函数
main "$@"
