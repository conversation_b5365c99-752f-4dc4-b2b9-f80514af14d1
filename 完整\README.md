# B2B贸易信息平台 - 完整版

这是一个基于 Next.js 15 和 Payload CMS 3 构建的企业级B2B贸易信息平台，集成了用户管理、产品管理、订单处理、支付系统、VIP订阅等完整功能。

## 🚀 技术栈

- **前端框架**: Next.js 15.2.4 (App Router)
- **后端CMS**: Payload CMS 3.49.0
- **数据库**: MongoDB with Mongoose
- **UI组件**: Radix UI + Tailwind CSS
- **文件存储**: UploadThing
- **支付集成**: PayPal, 支付宝, 微信支付
- **部署**: 宝塔面板 + PM2

## 📁 项目结构

```
完整/
├── src/
│   ├── collections/          # Payload CMS 集合
│   │   ├── Users.ts         # 用户管理
│   │   ├── Products.ts      # 产品管理
│   │   ├── Companies.ts     # 企业管理
│   │   ├── Orders.ts        # 订单管理
│   │   ├── Payments.ts      # 支付记录
│   │   ├── Subscriptions.ts # VIP订阅
│   │   ├── PointsTransactions.ts # 积分交易
│   │   ├── AccessLogs.ts    # 访问日志
│   │   ├── Categories.ts    # 产品分类
│   │   └── Media.ts         # 媒体文件
│   ├── globals/             # 全局配置
│   │   ├── Settings.ts      # 系统设置
│   │   ├── Navigation.ts    # 导航管理
│   │   └── SiteConfig.ts    # 站点配置
│   ├── access/              # 访问控制
│   │   ├── index.ts         # 访问控制函数
│   │   └── checkRole.ts     # 角色检查工具
│   └── hooks/               # 钩子函数
│       └── loginAfterCreate.ts # 注册后自动登录
├── payload.config.ts        # Payload CMS 配置
├── next.config.mjs         # Next.js 配置
├── package.json            # 依赖配置
├── ecosystem.config.js     # PM2 配置
├── deploy.sh              # 宝塔部署脚本
└── .env.example           # 环境变量示例
```

## 🎯 核心功能

### 用户系统
- ✅ 用户注册/登录/邮箱验证
- ✅ 角色管理 (管理员/用户/供应商/采购商)
- ✅ VIP等级系统 (Basic/Pro/Enterprise)
- ✅ 积分系统和奖励机制
- ✅ 企业认证和资质管理

### 产品管理
- ✅ 产品发布和管理
- ✅ 多级分类系统
- ✅ 规格参数和价格管理
- ✅ 库存管理和MOQ设置
- ✅ 访问级别控制 (公开/VIP/企业)

### 订单系统
- ✅ 订单创建和状态跟踪
- ✅ 支付集成 (PayPal/支付宝/微信)
- ✅ 物流跟踪和配送管理
- ✅ 订单时间线和操作记录

### VIP订阅
- ✅ 多种订阅计划 (月付/季付/年付)
- ✅ 自动续费和试用期
- ✅ 功能权限控制
- ✅ 订阅历史和支付记录

### 企业管理
- ✅ 企业资料和认证
- ✅ 营业执照和资质证书
- ✅ 信用评级系统
- ✅ 企业统计和分析

### 系统管理
- ✅ 访问日志和安全监控
- ✅ 系统设置和配置管理
- ✅ 导航和页面管理
- ✅ 邮件模板和通知

## 🛠️ 安装和部署

### 本地开发

1. **克隆项目**
```bash
cd 完整
npm install
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和第三方服务
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **访问应用**
- 前端: http://localhost:3000
- 管理后台: http://localhost:3000/admin

### 宝塔面板部署

1. **运行部署脚本**
```bash
chmod +x deploy.sh
./deploy.sh
```

2. **配置环境变量**
- 在宝塔面板中配置生产环境变量
- 确保MongoDB和Redis服务正常运行

3. **启动应用**
```bash
pm2 start ecosystem.config.js
```

## 🔧 配置说明

### 数据库配置
- MongoDB 连接字符串
- 数据库名称和认证信息

### 第三方服务
- UploadThing 文件存储
- PayPal 支付集成
- 邮件服务配置
- 短信服务配置

### 安全设置
- JWT 密钥配置
- CORS 域名设置
- 密码策略配置
- 登录限制设置

## 📊 数据模型

### 用户 (Users)
- 基本信息、角色权限
- VIP等级、积分余额
- 企业资料、认证状态

### 产品 (Products)
- 产品信息、规格参数
- 价格库存、访问控制
- SEO优化、统计数据

### 订单 (Orders)
- 订单详情、状态跟踪
- 支付信息、物流跟踪
- 时间线记录

### 企业 (Companies)
- 企业资料、联系信息
- 营业信息、资质证书
- 认证状态、信用评级

## 🎨 UI组件

项目使用 Radix UI 和 Tailwind CSS 构建现代化的用户界面：

- 响应式设计，支持移动端
- 无障碍访问支持
- 暗色模式支持
- 国际化支持 (中文/英文)

## 🔐 权限系统

### 角色权限
- **管理员**: 全部权限
- **用户**: 基础功能
- **供应商**: 产品管理
- **采购商**: 订单管理

### VIP权限
- **Basic**: 基础功能
- **Pro**: 高级功能
- **Enterprise**: 企业功能

### 访问控制
- 基于角色的访问控制 (RBAC)
- 基于VIP等级的功能控制
- 基于积分的内容访问
- 企业认证要求

## 📈 监控和分析

- 用户行为跟踪
- 访问日志记录
- 性能监控
- 错误日志收集
- 业务数据统计

## 🚀 部署优化

### 性能优化
- Next.js 静态生成
- 图片优化和压缩
- CDN 加速配置
- 数据库索引优化

### 安全加固
- HTTPS 强制跳转
- 安全头配置
- 输入验证和过滤
- 访问频率限制

## 📞 技术支持

如有问题或需要技术支持，请联系开发团队。

---

**版本**: 1.0.0  
**更新时间**: 2024-07-29  
**开发团队**: B2B贸易平台开发组
