import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../payload.config'

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const paymentStatus = searchParams.get('paymentStatus') || ''
    const shippingStatus = searchParams.get('shippingStatus') || ''
    const dateFrom = searchParams.get('dateFrom') || ''
    const dateTo = searchParams.get('dateTo') || ''
    const amountMin = searchParams.get('amountMin') || ''
    const amountMax = searchParams.get('amountMax') || ''
    const sortField = searchParams.get('sortField') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.or = [
        { orderNumber: { contains: search } },
        { 'user.name': { contains: search } },
        { 'user.email': { contains: search } },
        { 'shippingAddress.name': { contains: search } },
        { 'shippingAddress.phone': { contains: search } }
      ]
    }
    
    if (status) {
      where.status = { equals: status }
    }
    
    if (paymentStatus) {
      where.paymentStatus = { equals: paymentStatus }
    }
    
    if (shippingStatus) {
      where.shippingStatus = { equals: shippingStatus }
    }

    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) where.createdAt.greater_than_equal = new Date(dateFrom)
      if (dateTo) where.createdAt.less_than_equal = new Date(dateTo)
    }

    if (amountMin || amountMax) {
      where.totalAmount = {}
      if (amountMin) where.totalAmount.greater_than_equal = parseFloat(amountMin)
      if (amountMax) where.totalAmount.less_than_equal = parseFloat(amountMax)
    }

    // 查询订单
    const orders = await payload.find({
      collection: 'orders',
      where,
      page,
      limit,
      sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
      depth: 3,
    })

    // 统计数据
    const totalOrders = await payload.count({
      collection: 'orders',
      where: {},
    })

    const pendingOrders = await payload.count({
      collection: 'orders',
      where: { status: { equals: 'pending' } },
    })

    const completedOrders = await payload.count({
      collection: 'orders',
      where: { status: { equals: 'completed' } },
    })

    const cancelledOrders = await payload.count({
      collection: 'orders',
      where: { status: { equals: 'cancelled' } },
    })

    // 计算总收入
    const revenueResult = await payload.find({
      collection: 'orders',
      where: { 
        and: [
          { status: { equals: 'completed' } },
          { paymentStatus: { equals: 'paid' } }
        ]
      },
      limit: 0, // 不返回文档，只要统计
    })

    let totalRevenue = 0
    if (revenueResult.docs.length > 0) {
      // 这里需要聚合计算，暂时用简单方式
      const allCompletedOrders = await payload.find({
        collection: 'orders',
        where: { 
          and: [
            { status: { equals: 'completed' } },
            { paymentStatus: { equals: 'paid' } }
          ]
        },
        limit: 1000, // 限制数量避免性能问题
      })
      
      totalRevenue = allCompletedOrders.docs.reduce((sum, order) => sum + (order.totalAmount || 0), 0)
    }

    return NextResponse.json({
      success: true,
      data: {
        orders: orders.docs,
        pagination: {
          page: orders.page,
          limit: orders.limit,
          totalPages: orders.totalPages,
          totalDocs: orders.totalDocs,
          hasNextPage: orders.hasNextPage,
          hasPrevPage: orders.hasPrevPage,
        },
        stats: {
          total: totalOrders.totalDocs,
          pending: pendingOrders.totalDocs,
          completed: completedOrders.totalDocs,
          cancelled: cancelledOrders.totalDocs,
          totalRevenue,
        },
      },
    })
  } catch (error) {
    console.error('获取订单列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取订单列表失败' },
      { status: 500 }
    )
  }
}

// 创建新订单（管理员代下单）
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // 验证必填字段
    if (!body.user || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { success: false, error: '用户和订单项为必填项' },
        { status: 400 }
      )
    }

    // 计算订单金额
    let subtotal = 0
    const processedItems = []

    for (const item of body.items) {
      if (!item.product || !item.quantity || item.quantity <= 0) {
        return NextResponse.json(
          { success: false, error: '订单项信息不完整' },
          { status: 400 }
        )
      }

      // 获取产品信息
      const product = await payload.findByID({
        collection: 'products',
        id: item.product,
      })

      if (!product) {
        return NextResponse.json(
          { success: false, error: `产品 ${item.product} 不存在` },
          { status: 400 }
        )
      }

      const itemTotal = product.price * item.quantity
      subtotal += itemTotal

      processedItems.push({
        product: item.product,
        quantity: item.quantity,
        price: product.price,
        total: itemTotal,
        title: product.title,
        sku: product.sku,
      })
    }

    const shippingFee = body.shippingFee || 0
    const tax = body.tax || 0
    const discount = body.discount || 0
    const totalAmount = subtotal + shippingFee + tax - discount

    // 生成订单号
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`

    // 创建订单
    const order = await payload.create({
      collection: 'orders',
      data: {
        orderNumber,
        user: body.user,
        items: processedItems,
        subtotal,
        shippingFee,
        tax,
        discount,
        totalAmount,
        currency: body.currency || 'CNY',
        status: body.status || 'pending',
        paymentStatus: body.paymentStatus || 'pending',
        paymentMethod: body.paymentMethod || '',
        shippingStatus: body.shippingStatus || 'pending',
        shippingMethod: body.shippingMethod || '',
        shippingAddress: body.shippingAddress || {},
        billingAddress: body.billingAddress || body.shippingAddress || {},
        notes: body.notes || '',
        adminNotes: body.adminNotes || '',
        trackingNumber: body.trackingNumber || '',
        estimatedDelivery: body.estimatedDelivery || null,
      },
    })

    return NextResponse.json({
      success: true,
      data: order,
      message: '订单创建成功',
    })
  } catch (error) {
    console.error('创建订单失败:', error)
    return NextResponse.json(
      { success: false, error: '创建订单失败' },
      { status: 500 }
    )
  }
}

// 批量操作订单
export async function PATCH(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { action, orderIds, data } = body

    if (!action || !orderIds || !Array.isArray(orderIds)) {
      return NextResponse.json(
        { success: false, error: '参数错误' },
        { status: 400 }
      )
    }

    const results = []

    for (const orderId of orderIds) {
      try {
        let result
        
        switch (action) {
          case 'confirm':
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { status: 'confirmed' },
            })
            break
            
          case 'ship':
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                status: 'shipped',
                shippingStatus: 'shipped',
                shippedAt: new Date(),
                trackingNumber: data?.trackingNumber || '',
              },
            })
            break
            
          case 'deliver':
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                status: 'delivered',
                shippingStatus: 'delivered',
                deliveredAt: new Date(),
              },
            })
            break
            
          case 'complete':
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                status: 'completed',
                completedAt: new Date(),
              },
            })
            break
            
          case 'cancel':
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                status: 'cancelled',
                cancelledAt: new Date(),
                cancelReason: data?.reason || '管理员取消',
              },
            })
            break
            
          case 'updatePaymentStatus':
            if (!data?.paymentStatus) {
              throw new Error('支付状态不能为空')
            }
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                paymentStatus: data.paymentStatus,
                paidAt: data.paymentStatus === 'paid' ? new Date() : null,
              },
            })
            break
            
          case 'updateShippingStatus':
            if (!data?.shippingStatus) {
              throw new Error('物流状态不能为空')
            }
            result = await payload.update({
              collection: 'orders',
              id: orderId,
              data: { 
                shippingStatus: data.shippingStatus,
                trackingNumber: data.trackingNumber || '',
              },
            })
            break
            
          default:
            throw new Error(`不支持的操作: ${action}`)
        }
        
        results.push({ orderId, success: true, data: result })
      } catch (error) {
        results.push({ 
          orderId, 
          success: false, 
          error: error instanceof Error ? error.message : '操作失败' 
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      data: results,
      message: `操作完成: ${successCount} 成功, ${failCount} 失败`,
    })
  } catch (error) {
    console.error('批量操作失败:', error)
    return NextResponse.json(
      { success: false, error: '批量操作失败' },
      { status: 500 }
    )
  }
}
