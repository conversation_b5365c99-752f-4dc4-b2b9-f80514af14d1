import type { Access, FieldAccess } from 'payload'
import { checkRole } from './checkRole'

// 任何人都可以访问
export const anyone: Access = () => true

// 只有管理员可以访问
export const admins: Access = ({ req: { user } }) => {
  return checkRole(['admin'], user)
}

// 管理员和当前用户可以访问
export const adminsAndUser: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return {
      id: {
        equals: user.id,
      },
    }
  }
  
  return false
}

// 已登录用户可以访问
export const loggedIn: Access = ({ req: { user } }) => {
  return Boolean(user)
}

// 管理员或供应商可以访问
export const adminsOrSuppliers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin', 'supplier'], user)) return true
  }
  
  return false
}

// 管理员或采购商可以访问
export const adminsOrBuyers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin', 'buyer'], user)) return true
  }
  
  return false
}

// VIP用户可以访问
export const vipUsers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    // 检查VIP等级
    const vipLevels = ['Pro', 'Enterprise']
    return vipLevels.includes(user.vipLevel)
  }
  
  return false
}

// 企业版用户可以访问
export const enterpriseUsers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return user.vipLevel === 'Enterprise'
  }
  
  return false
}

// 已验证用户可以访问
export const verifiedUsers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return user.verified === true
  }
  
  return false
}

// 字段级访问控制

// 只有管理员可以读取的字段
export const adminsFieldRead: FieldAccess = ({ req: { user } }) => {
  return checkRole(['admin'], user)
}

// 只有管理员可以更新的字段
export const adminsFieldUpdate: FieldAccess = ({ req: { user } }) => {
  return checkRole(['admin'], user)
}

// 管理员和当前用户可以读取的字段
export const adminsAndUserFieldRead: FieldAccess = ({ req: { user }, doc }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return doc?.id === user.id
  }
  
  return false
}

// 管理员和当前用户可以更新的字段
export const adminsAndUserFieldUpdate: FieldAccess = ({ req: { user }, doc }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return doc?.id === user.id
  }
  
  return false
}

// 供应商可以访问自己的产品
export const supplierProducts: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    if (checkRole(['supplier'], user)) {
      return {
        supplier: {
          equals: user.id,
        },
      }
    }
  }
  
  return false
}

// 用户可以访问自己的订单
export const userOrders: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return {
      or: [
        {
          buyer: {
            equals: user.id,
          },
        },
        {
          'product.supplier': {
            equals: user.id,
          },
        },
      ],
    }
  }
  
  return false
}

// 根据产品访问级别控制访问
export const productAccessLevel: Access = ({ req: { user } }) => {
  // 管理员可以访问所有产品
  if (user && checkRole(['admin'], user)) return true
  
  // 未登录用户只能访问公开产品
  if (!user) {
    return {
      accessLevel: {
        equals: 'public',
      },
    }
  }
  
  // 根据用户VIP等级控制访问
  const accessLevels = ['public']
  
  if (user.vipLevel === 'Pro' || user.vipLevel === 'Enterprise') {
    accessLevels.push('vip')
  }
  
  if (user.vipLevel === 'Enterprise') {
    accessLevels.push('enterprise')
  }
  
  return {
    accessLevel: {
      in: accessLevels,
    },
  }
}

// 根据用户积分控制访问
export const pointsBasedAccess = (requiredPoints: number): Access => {
  return ({ req: { user } }) => {
    if (user) {
      if (checkRole(['admin'], user)) return true
      
      return user.points >= requiredPoints
    }
    
    return false
  }
}

// 根据用户状态控制访问
export const activeUsers: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return user.status === 'active'
  }
  
  return false
}

// 企业认证用户可以访问
export const verifiedCompanies: Access = ({ req: { user } }) => {
  if (user) {
    if (checkRole(['admin'], user)) return true
    
    return user.companyProfile?.verificationStatus === 'verified'
  }
  
  return false
}
