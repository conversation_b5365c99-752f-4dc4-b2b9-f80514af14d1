import type { User } from '../../payload-types'

/**
 * 检查用户是否具有指定角色
 * @param allRoles 允许的角色数组
 * @param user 用户对象
 * @returns 是否具有权限
 */
export const checkRole = (allRoles: string[] = [], user?: User | null): boolean => {
  if (!user) return false
  
  // 如果用户没有角色，返回false
  if (!user.role || !Array.isArray(user.role)) return false
  
  // 检查用户是否具有任一指定角色
  return allRoles.some((role) => user.role?.includes(role))
}

/**
 * 检查用户是否具有所有指定角色
 * @param allRoles 必须具备的所有角色
 * @param user 用户对象
 * @returns 是否具有所有权限
 */
export const checkAllRoles = (allRoles: string[] = [], user?: User | null): boolean => {
  if (!user) return false
  
  if (!user.role || !Array.isArray(user.role)) return false
  
  // 检查用户是否具有所有指定角色
  return allRoles.every((role) => user.role?.includes(role))
}

/**
 * 检查用户VIP等级
 * @param requiredLevel 所需的VIP等级
 * @param user 用户对象
 * @returns 是否满足VIP等级要求
 */
export const checkVipLevel = (requiredLevel: string, user?: User | null): boolean => {
  if (!user) return false
  
  const vipHierarchy = {
    'Basic': 1,
    'Pro': 2,
    'Enterprise': 3,
  }
  
  const userLevel = vipHierarchy[user.vipLevel as keyof typeof vipHierarchy] || 0
  const required = vipHierarchy[requiredLevel as keyof typeof vipHierarchy] || 0
  
  return userLevel >= required
}

/**
 * 检查用户积分是否足够
 * @param requiredPoints 所需积分
 * @param user 用户对象
 * @returns 是否有足够积分
 */
export const checkPoints = (requiredPoints: number, user?: User | null): boolean => {
  if (!user) return false
  
  return (user.points || 0) >= requiredPoints
}

/**
 * 检查用户是否已验证
 * @param user 用户对象
 * @returns 是否已验证
 */
export const checkVerified = (user?: User | null): boolean => {
  if (!user) return false
  
  return user.verified === true
}

/**
 * 检查用户状态是否正常
 * @param user 用户对象
 * @returns 状态是否正常
 */
export const checkActiveStatus = (user?: User | null): boolean => {
  if (!user) return false
  
  return user.status === 'active'
}

/**
 * 检查企业认证状态
 * @param user 用户对象
 * @returns 企业是否已认证
 */
export const checkCompanyVerification = (user?: User | null): boolean => {
  if (!user) return false
  
  return user.companyProfile?.verificationStatus === 'verified'
}

/**
 * 综合权限检查
 * @param options 权限检查选项
 * @param user 用户对象
 * @returns 是否通过权限检查
 */
export const checkPermissions = (
  options: {
    roles?: string[]
    allRoles?: string[]
    vipLevel?: string
    points?: number
    verified?: boolean
    active?: boolean
    companyVerified?: boolean
  },
  user?: User | null
): boolean => {
  if (!user) return false
  
  // 检查角色（任一）
  if (options.roles && !checkRole(options.roles, user)) {
    return false
  }
  
  // 检查角色（全部）
  if (options.allRoles && !checkAllRoles(options.allRoles, user)) {
    return false
  }
  
  // 检查VIP等级
  if (options.vipLevel && !checkVipLevel(options.vipLevel, user)) {
    return false
  }
  
  // 检查积分
  if (options.points && !checkPoints(options.points, user)) {
    return false
  }
  
  // 检查验证状态
  if (options.verified && !checkVerified(user)) {
    return false
  }
  
  // 检查用户状态
  if (options.active && !checkActiveStatus(user)) {
    return false
  }
  
  // 检查企业认证
  if (options.companyVerified && !checkCompanyVerification(user)) {
    return false
  }
  
  return true
}

/**
 * 获取用户权限级别
 * @param user 用户对象
 * @returns 权限级别数值
 */
export const getUserPermissionLevel = (user?: User | null): number => {
  if (!user) return 0
  
  let level = 1 // 基础用户
  
  // 角色权限
  if (checkRole(['admin'], user)) level = 100
  else if (checkRole(['supplier'], user)) level += 10
  else if (checkRole(['buyer'], user)) level += 5
  
  // VIP等级权限
  if (user.vipLevel === 'Enterprise') level += 20
  else if (user.vipLevel === 'Pro') level += 10
  
  // 验证状态权限
  if (checkVerified(user)) level += 5
  if (checkCompanyVerification(user)) level += 10
  
  return level
}

/**
 * 检查用户是否可以访问特定功能
 * @param feature 功能名称
 * @param user 用户对象
 * @returns 是否可以访问
 */
export const checkFeatureAccess = (feature: string, user?: User | null): boolean => {
  if (!user) return false
  
  const featurePermissions: Record<string, any> = {
    // 基础功能
    'view_products': { roles: ['user', 'buyer', 'supplier', 'admin'] },
    'search_products': { roles: ['user', 'buyer', 'supplier', 'admin'] },
    
    // 供应商功能
    'create_products': { roles: ['supplier', 'admin'] },
    'manage_products': { roles: ['supplier', 'admin'] },
    'view_analytics': { roles: ['supplier', 'admin'], vipLevel: 'Pro' },
    
    // 采购商功能
    'send_inquiries': { roles: ['buyer', 'admin'], verified: true },
    'bulk_inquiries': { roles: ['buyer', 'admin'], vipLevel: 'Pro' },
    
    // VIP功能
    'advanced_search': { vipLevel: 'Pro' },
    'export_data': { vipLevel: 'Pro' },
    'priority_support': { vipLevel: 'Enterprise' },
    'custom_reports': { vipLevel: 'Enterprise' },
    
    // 管理员功能
    'admin_panel': { roles: ['admin'] },
    'user_management': { roles: ['admin'] },
    'system_settings': { roles: ['admin'] },
  }
  
  const permission = featurePermissions[feature]
  if (!permission) return false
  
  return checkPermissions(permission, user)
}
