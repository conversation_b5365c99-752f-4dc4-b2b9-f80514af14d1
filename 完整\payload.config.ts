import { mongoose<PERSON>dapter } from '@payloadcms/db-mongodb'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { uploadthingStorage } from '@payloadcms/storage-uploadthing'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import path from 'path'
import { fileURLToPath } from 'url'

// Collections
import { Users } from './src/collections/Users'
import { Products } from './src/collections/Products'
import { Companies } from './src/collections/Companies'
import { Orders } from './src/collections/Orders'
import { Categories } from './src/collections/Categories'
import { Media } from './src/collections/Media'
import { Payments } from './src/collections/Payments'
import { Subscriptions } from './src/collections/Subscriptions'

// Globals
import { Settings } from './src/globals/Settings'
import { Navigation } from './src/globals/Navigation'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
    meta: {
      titleSuffix: '- B2B Trade Platform',
      favicon: '/favicon.ico',
      ogImage: '/og-image.jpg',
    },
    components: {
      // Custom admin components can be added here
    },
  },
  collections: [
    Users,
    Products,
    Companies,
    Orders,
    Categories,
    Media,
    Payments,
    Subscriptions,
  ],
  globals: [
    Settings,
    Navigation,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/b2b-trade-platform',
  }),
  sharp,
  plugins: [
    uploadthingStorage({
      collections: {
        media: true,
      },
      options: {
        token: process.env.UPLOADTHING_TOKEN,
        acl: 'public-read',
      },
    }),
  ],
  cors: [
    process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3001',
  ].filter(Boolean),
  csrf: [
    process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3001',
  ].filter(Boolean),
  endpoints: [
    // Custom endpoints can be added here
  ],
  graphQL: {
    schemaOutputFile: path.resolve(dirname, 'generated-schema.graphql'),
  },
  upload: {
    limits: {
      fileSize: 5000000, // 5MB
    },
  },
  rateLimit: {
    max: 2000,
  },
  async onInit(payload) {
    // Seed data or perform initialization tasks
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    })

    if (existingUsers.docs.length === 0) {
      await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'admin123',
          role: 'admin',
          name: 'Admin User',
        },
      })
    }
  },
})
