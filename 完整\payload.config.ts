import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { uploadthingStorage } from '@payloadcms/storage-uploadthing'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import path from 'path'
import { fileURLToPath } from 'url'

// Collections
import { Users } from './src/collections/Users'
import { Products } from './src/collections/Products'
import { Companies } from './src/collections/Companies'
import { Orders } from './src/collections/Orders'
import { Categories } from './src/collections/Categories'
import { Media } from './src/collections/Media'
import { Payments } from './src/collections/Payments'
import { Subscriptions } from './src/collections/Subscriptions'
import { PointsTransactions } from './src/collections/PointsTransactions'
import { AccessLogs } from './src/collections/AccessLogs'

// Globals
import { Settings } from './src/globals/Settings'
import { Navigation } from './src/globals/Navigation'
import { SiteConfig } from './src/globals/SiteConfig'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
    meta: {
      titleSuffix: '- B2B贸易平台管理后台',
      favicon: '/favicon.ico',
      ogImage: '/og-image.jpg',
    },
    components: {
      // 自定义管理后台组件
      beforeDashboard: ['@/components/admin/CustomDashboard'],
      beforeLogin: ['@/components/admin/CustomLogin'],
    },
    css: path.resolve(dirname, 'src/styles/admin.css'),
    dateFormat: 'yyyy-MM-dd HH:mm:ss',
    livePreview: {
      breakpoints: [
        {
          label: '手机',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: '平板',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: '桌面',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  collections: [
    Users,
    Products,
    Companies,
    Orders,
    Categories,
    Media,
    Payments,
    Subscriptions,
    PointsTransactions,
    AccessLogs,
  ],
  globals: [
    Settings,
    Navigation,
    SiteConfig,
  ],
  editor: lexicalEditor({
    features: ({ defaultFeatures }) => [
      ...defaultFeatures,
      // 可以添加自定义编辑器功能
    ],
  }),
  secret: process.env.PAYLOAD_SECRET || 'your-super-secret-key-change-in-production',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/b2b-trade-platform',
    connectOptions: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  }),
  sharp,
  plugins: [
    uploadthingStorage({
      collections: {
        media: true,
      },
      options: {
        token: process.env.UPLOADTHING_TOKEN,
        acl: 'public-read',
      },
    }),
  ],
  cors: [
    process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3001',
    process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
    // 宝塔部署时的域名
    process.env.PRODUCTION_URL || '',
  ].filter(Boolean),
  csrf: [
    process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3001',
    process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
    process.env.PRODUCTION_URL || '',
  ].filter(Boolean),
  endpoints: [
    // 自定义API端点
    {
      path: '/api/custom/stats',
      method: 'get',
      handler: async (req, res) => {
        // 统计数据API
        const stats = {
          totalUsers: await req.payload.count({ collection: 'users' }),
          totalProducts: await req.payload.count({ collection: 'products' }),
          totalOrders: await req.payload.count({ collection: 'orders' }),
          totalRevenue: 0, // 计算总收入
        }
        res.json(stats)
      },
    },
  ],
  graphQL: {
    schemaOutputFile: path.resolve(dirname, 'generated-schema.graphql'),
  },
  upload: {
    limits: {
      fileSize: 10000000, // 10MB
    },
  },
  rateLimit: {
    max: 2000,
    windowMs: 15 * 60 * 1000, // 15分钟
  },
  localization: {
    locales: ['zh', 'en'],
    defaultLocale: 'zh',
    fallback: true,
  },
  async onInit(payload) {
    // 初始化数据
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    })

    if (existingUsers.docs.length === 0) {
      // 创建默认管理员用户
      await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'admin123456',
          role: ['admin'],
          name: '系统管理员',
          company: '平台运营',
          vipLevel: 'Enterprise',
          points: 10000,
          verified: true,
        },
      })

      // 创建默认分类
      const categories = [
        { name: '电子产品', slug: 'electronics', description: '电子设备和组件' },
        { name: '机械设备', slug: 'machinery', description: '工业机械和设备' },
        { name: '化工原料', slug: 'chemicals', description: '化工产品和原材料' },
        { name: '纺织服装', slug: 'textiles', description: '纺织品和服装' },
        { name: '建筑材料', slug: 'construction', description: '建筑和装修材料' },
      ]

      for (const category of categories) {
        await payload.create({
          collection: 'categories',
          data: category,
        })
      }

      // 初始化网站配置
      await payload.updateGlobal({
        slug: 'site-config',
        data: {
          siteName: 'B2B贸易信息平台',
          siteDescription: '专业的B2B贸易信息平台，连接全球供应商和采购商',
          contactEmail: '<EMAIL>',
          supportPhone: '+86-************',
          address: '中国上海市浦东新区',
          socialMedia: {
            wechat: 'b2btrade_official',
            weibo: '@B2B贸易平台',
            linkedin: 'b2b-trade-platform',
          },
        },
      })
    }
  },
})
