import type { CollectionConfig } from 'payload'
import { admins, userOrders } from '../access/index'

export const Orders: CollectionConfig = {
  slug: 'orders',
  admin: {
    useAsTitle: 'orderNumber',
    defaultColumns: ['orderNumber', 'buyer', 'supplier', 'status', 'totalAmount', 'createdAt'],
    listSearchableFields: ['orderNumber', 'notes'],
    group: '订单管理',
  },
  access: {
    read: userOrders,
    create: userOrders,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role?.includes('admin')) return true
      
      return {
        or: [
          {
            buyer: {
              equals: user.id,
            },
          },
          {
            supplier: {
              equals: user.id,
            },
          },
        ],
      }
    },
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 生成订单号
          if (!data.orderNumber) {
            const timestamp = Date.now()
            const random = Math.random().toString(36).substr(2, 5).toUpperCase()
            data.orderNumber = `ORD-${timestamp}-${random}`
          }
          
          // 设置默认状态
          data.status = data.status || 'pending'
          
          // 设置买家
          if (!data.buyer && req.user) {
            data.buyer = req.user.id
          }
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'orderNumber',
      label: '订单号',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'buyer',
      label: '买家',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'supplier',
      label: '供应商',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'items',
      label: '订单商品',
      type: 'array',
      minRows: 1,
      fields: [
        {
          name: 'product',
          label: '产品',
          type: 'relationship',
          relationTo: 'products',
          required: true,
        },
        {
          name: 'quantity',
          label: '数量',
          type: 'number',
          required: true,
          min: 1,
        },
        {
          name: 'unitPrice',
          label: '单价',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'totalPrice',
          label: '小计',
          type: 'number',
          admin: {
            readOnly: true,
          },
          hooks: {
            beforeChange: [
              ({ siblingData }) => {
                return (siblingData.quantity || 0) * (siblingData.unitPrice || 0)
              },
            ],
          },
        },
        {
          name: 'specifications',
          label: '规格要求',
          type: 'textarea',
        },
        {
          name: 'notes',
          label: '备注',
          type: 'textarea',
        },
      ],
    },
    {
      name: 'subtotal',
      label: '小计金额',
      type: 'number',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'shippingFee',
      label: '运费',
      type: 'number',
      defaultValue: 0,
      min: 0,
    },
    {
      name: 'tax',
      label: '税费',
      type: 'number',
      defaultValue: 0,
      min: 0,
    },
    {
      name: 'discount',
      label: '折扣',
      type: 'number',
      defaultValue: 0,
      min: 0,
    },
    {
      name: 'totalAmount',
      label: '总金额',
      type: 'number',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'currency',
      label: '货币',
      type: 'select',
      defaultValue: 'CNY',
      options: [
        { label: '人民币 (CNY)', value: 'CNY' },
        { label: '美元 (USD)', value: 'USD' },
        { label: '欧元 (EUR)', value: 'EUR' },
      ],
    },
    {
      name: 'status',
      label: '订单状态',
      type: 'select',
      defaultValue: 'pending',
      options: [
        { label: '待确认', value: 'pending' },
        { label: '已确认', value: 'confirmed' },
        { label: '生产中', value: 'production' },
        { label: '已发货', value: 'shipped' },
        { label: '已送达', value: 'delivered' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' },
        { label: '退款中', value: 'refunding' },
        { label: '已退款', value: 'refunded' },
      ],
    },
    {
      name: 'paymentStatus',
      label: '支付状态',
      type: 'select',
      defaultValue: 'unpaid',
      options: [
        { label: '未支付', value: 'unpaid' },
        { label: '部分支付', value: 'partial' },
        { label: '已支付', value: 'paid' },
        { label: '退款中', value: 'refunding' },
        { label: '已退款', value: 'refunded' },
      ],
    },
    {
      name: 'shippingInfo',
      label: '配送信息',
      type: 'group',
      fields: [
        {
          name: 'method',
          label: '配送方式',
          type: 'select',
          options: [
            { label: '快递', value: 'express' },
            { label: '物流', value: 'logistics' },
            { label: '自提', value: 'pickup' },
            { label: '海运', value: 'sea' },
            { label: '空运', value: 'air' },
          ],
        },
        {
          name: 'address',
          label: '收货地址',
          type: 'group',
          fields: [
            {
              name: 'recipient',
              label: '收货人',
              type: 'text',
            },
            {
              name: 'phone',
              label: '联系电话',
              type: 'text',
            },
            {
              name: 'address',
              label: '详细地址',
              type: 'textarea',
            },
            {
              name: 'zipCode',
              label: '邮编',
              type: 'text',
            },
          ],
        },
        {
          name: 'trackingNumber',
          label: '快递单号',
          type: 'text',
        },
        {
          name: 'carrier',
          label: '承运商',
          type: 'text',
        },
        {
          name: 'estimatedDelivery',
          label: '预计送达时间',
          type: 'date',
        },
        {
          name: 'actualDelivery',
          label: '实际送达时间',
          type: 'date',
        },
      ],
    },
    {
      name: 'timeline',
      label: '订单时间线',
      type: 'array',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'status',
          label: '状态',
          type: 'text',
        },
        {
          name: 'timestamp',
          label: '时间',
          type: 'date',
        },
        {
          name: 'notes',
          label: '备注',
          type: 'text',
        },
        {
          name: 'operator',
          label: '操作人',
          type: 'relationship',
          relationTo: 'users',
        },
      ],
    },
    {
      name: 'notes',
      label: '订单备注',
      type: 'textarea',
    },
    {
      name: 'internalNotes',
      label: '内部备注',
      type: 'textarea',
      access: {
        read: admins,
        update: admins,
      },
    },
    {
      name: 'attachments',
      label: '附件',
      type: 'array',
      fields: [
        {
          name: 'file',
          label: '文件',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'description',
          label: '描述',
          type: 'text',
        },
      ],
    },
    {
      name: 'priority',
      label: '优先级',
      type: 'select',
      defaultValue: 'normal',
      options: [
        { label: '低', value: 'low' },
        { label: '正常', value: 'normal' },
        { label: '高', value: 'high' },
        { label: '紧急', value: 'urgent' },
      ],
    },
    {
      name: 'source',
      label: '订单来源',
      type: 'select',
      defaultValue: 'website',
      options: [
        { label: '网站', value: 'website' },
        { label: '手机APP', value: 'mobile' },
        { label: '电话', value: 'phone' },
        { label: '邮件', value: 'email' },
        { label: '线下', value: 'offline' },
      ],
    },
  ],
  timestamps: true,
}
