'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Shield, Users, Package, TrendingUp, Globe, Lock, Zap } from 'lucide-react'

export default function CustomLogin() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 顶部品牌信息 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl">
              <Shield className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            B2B贸易平台管理后台
          </h1>
          <p className="text-gray-600 max-w-md mx-auto">
            专业的企业级B2B贸易管理系统，为您的业务提供全方位的数字化解决方案
          </p>
        </div>

        {/* 功能特色展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center pb-4">
              <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-3">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg">用户管理</CardTitle>
              <CardDescription>
                完整的用户生命周期管理，支持多角色权限控制
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>用户注册与认证</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>VIP等级管理</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>积分系统</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center pb-4">
              <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-3">
                <Package className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-lg">产品管理</CardTitle>
              <CardDescription>
                智能化产品管理系统，支持多规格多价格体系
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>产品发布审核</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>库存管理</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>价格策略</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center pb-4">
              <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle className="text-lg">数据分析</CardTitle>
              <CardDescription>
                实时业务数据分析，助力科学决策
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>销售统计</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>用户行为分析</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>财务报表</span>
                  <Badge variant="secondary">✓</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 系统优势 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
          <h2 className="text-2xl font-bold text-center text-gray-900 mb-8">
            为什么选择我们的管理系统？
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-full w-fit mx-auto mb-4">
                <Globe className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                全球化支持
              </h3>
              <p className="text-gray-600 text-sm">
                支持多语言、多货币、多时区，满足国际化业务需求
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-full w-fit mx-auto mb-4">
                <Lock className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                企业级安全
              </h3>
              <p className="text-gray-600 text-sm">
                银行级安全防护，数据加密传输，确保业务数据安全
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-full w-fit mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                高性能架构
              </h3>
              <p className="text-gray-600 text-sm">
                基于现代化技术栈，支持高并发，响应速度快
              </p>
            </div>
          </div>
        </div>

        {/* 技术栈信息 */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-xl">技术架构</CardTitle>
            <CardDescription>
              采用最新的技术栈，确保系统的稳定性和可扩展性
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="font-semibold text-gray-900">Next.js 15</div>
                <div className="text-sm text-gray-600">前端框架</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="font-semibold text-gray-900">Payload CMS</div>
                <div className="text-sm text-gray-600">内容管理</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="font-semibold text-gray-900">MongoDB</div>
                <div className="text-sm text-gray-600">数据库</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="font-semibold text-gray-900">TypeScript</div>
                <div className="text-sm text-gray-600">开发语言</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 底部信息 */}
        <div className="text-center mt-12 text-gray-500 text-sm">
          <p>© 2024 B2B贸易平台. 保留所有权利.</p>
          <p className="mt-1">
            如需技术支持，请联系系统管理员
          </p>
        </div>
      </div>
    </div>
  )
}
