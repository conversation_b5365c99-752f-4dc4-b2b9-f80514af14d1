'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import CustomHeader from './CustomHeader'
import CustomNavigation from './CustomNavigation'

interface AdminLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  showSearch?: boolean
  showActions?: boolean
}

export default function AdminLayout({ 
  children, 
  title,
  subtitle,
  showSearch = true,
  showActions = true
}: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">B2B</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                贸易平台
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                管理后台
              </p>
            </div>
          </div>
          
          {/* 移动端关闭按钮 */}
          <button
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={() => setSidebarOpen(false)}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 导航菜单 */}
        <div className="flex-1 overflow-y-auto">
          <CustomNavigation />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:ml-64">
        {/* 头部 */}
        <CustomHeader
          title={title}
          subtitle={subtitle}
          onMenuToggle={() => setSidebarOpen(true)}
          showSearch={showSearch}
          showActions={showActions}
        />

        {/* 主要内容 */}
        <main className="flex-1">
          <div className="p-6">
            {children}
          </div>
        </main>

        {/* 页脚 */}
        <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>© 2024 B2B贸易平台</span>
              <span>•</span>
              <span>版本 1.0.0</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <a href="#" className="hover:text-gray-700 dark:hover:text-gray-300">
                帮助文档
              </a>
              <span>•</span>
              <a href="#" className="hover:text-gray-700 dark:hover:text-gray-300">
                技术支持
              </a>
              <span>•</span>
              <a href="#" className="hover:text-gray-700 dark:hover:text-gray-300">
                反馈建议
              </a>
            </div>
          </div>
        </footer>
      </div>

      {/* 快捷键提示 */}
      <div className="fixed bottom-4 right-4 z-30">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 text-xs text-gray-500 dark:text-gray-400 max-w-xs">
          <div className="font-medium mb-1">快捷键</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>搜索</span>
              <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+K</kbd>
            </div>
            <div className="flex justify-between">
              <span>新建</span>
              <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+N</kbd>
            </div>
            <div className="flex justify-between">
              <span>刷新</span>
              <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">F5</kbd>
            </div>
          </div>
        </div>
      </div>

      {/* 全局快捷键处理 */}
      <div className="sr-only">
        <button
          onKeyDown={(e) => {
            if (e.ctrlKey || e.metaKey) {
              switch (e.key) {
                case 'k':
                  e.preventDefault()
                  // 聚焦搜索框
                  const searchInput = document.querySelector('input[placeholder*="搜索"]') as HTMLInputElement
                  if (searchInput) {
                    searchInput.focus()
                  }
                  break
                case 'n':
                  e.preventDefault()
                  // 触发新建操作
                  console.log('New item shortcut')
                  break
                default:
                  break
              }
            }
          }}
          tabIndex={-1}
        />
      </div>
    </div>
  )
}
