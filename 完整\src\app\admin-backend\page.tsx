'use client'

import { useState, useEffect } from 'react'
import { AdminLayout } from '@/components/admin/AdminLayout'
import { DataTable } from '@/components/admin/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Package, 
  ShoppingCart, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Download,
  RefreshCw,
  Plus,
  Filter,
  Search
} from 'lucide-react'

interface DashboardStats {
  overview: {
    users: { total: number; new: number; active: number; vip: number }
    products: { total: number; published: number; featured: number; lowStock: number }
    orders: { total: number; new: number; pending: number; completed: number }
    revenue: { total: number; period: number; currency: string }
    payments: { total: number; successful: number; failed: number; successRate: number }
    subscriptions: { total: number; active: number }
    companies: { total: number; verified: number }
  }
  recentActivity: {
    users: any[]
    orders: any[]
    payments: any[]
  }
  systemStatus: {
    database: string
    storage: string
    payment: string
    email: string
  }
  trends: Array<{
    date: string
    users: number
    orders: number
    revenue: number
  }>
}

export default function AdminBackendPage() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [users, setUsers] = useState([])
  const [products, setProducts] = useState([])
  const [orders, setOrders] = useState([])
  const [companies, setCompanies] = useState([])
  const [settings, setSettings] = useState(null)
  const [systemLogs, setSystemLogs] = useState([])

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/dashboard')
      const result = await response.json()
      if (result.success) {
        setDashboardData(result.data)
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取用户数据
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=20')
      const result = await response.json()
      if (result.success) {
        setUsers(result.data.users)
      }
    } catch (error) {
      console.error('获取用户数据失败:', error)
    }
  }

  // 获取产品数据
  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/admin/products?limit=20')
      const result = await response.json()
      if (result.success) {
        setProducts(result.data.products)
      }
    } catch (error) {
      console.error('获取产品数据失败:', error)
    }
  }

  // 获取订单数据
  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/admin/orders?limit=20')
      const result = await response.json()
      if (result.success) {
        setOrders(result.data.orders)
      }
    } catch (error) {
      console.error('获取订单数据失败:', error)
    }
  }

  // 获取公司数据
  const fetchCompanies = async () => {
    try {
      const response = await fetch('/api/admin/companies?limit=20')
      const result = await response.json()
      if (result.success) {
        setCompanies(result.data.companies)
      }
    } catch (error) {
      console.error('获取公司数据失败:', error)
    }
  }

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings')
      const result = await response.json()
      if (result.success) {
        setSettings(result.data)
      }
    } catch (error) {
      console.error('获取系统设置失败:', error)
    }
  }

  // 获取系统日志
  const fetchSystemLogs = async () => {
    try {
      const response = await fetch('/api/admin/settings?type=all&limit=50', {
        method: 'PATCH'
      })
      const result = await response.json()
      if (result.success) {
        setSystemLogs(result.data.logs)
      }
    } catch (error) {
      console.error('获取系统日志失败:', error)
    }
  }

  useEffect(() => {
    fetchDashboardData()
    fetchUsers()
    fetchProducts()
    fetchOrders()
    fetchCompanies()
    fetchSettings()
    fetchSystemLogs()
  }, [])

  // 用户表格列配置
  const userColumns = [
    {
      key: 'name',
      title: '姓名',
      render: (value: string, record: any) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-600">
              {record.name?.charAt(0) || record.email?.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium">{record.name || '未设置'}</div>
            <div className="text-sm text-gray-500">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      title: '角色',
      render: (value: string) => (
        <Badge variant={value === 'admin' ? 'default' : 'secondary'}>
          {value === 'admin' ? '管理员' : value === 'user' ? '用户' : value}
        </Badge>
      ),
    },
    {
      key: 'vipLevel',
      title: 'VIP等级',
      render: (value: string) => (
        <Badge variant={value === 'basic' ? 'outline' : 'default'}>
          {value === 'basic' ? '基础' : value === 'pro' ? '专业' : value === 'enterprise' ? '企业' : value}
        </Badge>
      ),
    },
    {
      key: 'status',
      title: '状态',
      render: (value: string) => (
        <Badge variant={value === 'active' ? 'default' : 'destructive'}>
          {value === 'active' ? '活跃' : '禁用'}
        </Badge>
      ),
    },
    {
      key: 'points',
      title: '积分',
      render: (value: number) => value || 0,
    },
    {
      key: 'createdAt',
      title: '注册时间',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ]

  // 产品表格列配置
  const productColumns = [
    {
      key: 'title',
      title: '产品名称',
      render: (value: string, record: any) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">SKU: {record.sku}</div>
        </div>
      ),
    },
    {
      key: 'category',
      title: '分类',
      render: (value: any) => value?.title || '未分类',
    },
    {
      key: 'price',
      title: '价格',
      render: (value: number) => `¥${value?.toFixed(2) || '0.00'}`,
    },
    {
      key: 'inventory',
      title: '库存',
      render: (value: number) => (
        <span className={value < 10 ? 'text-red-600' : ''}>
          {value || 0}
        </span>
      ),
    },
    {
      key: 'status',
      title: '状态',
      render: (value: string) => (
        <Badge variant={value === 'published' ? 'default' : 'secondary'}>
          {value === 'published' ? '已发布' : '草稿'}
        </Badge>
      ),
    },
    {
      key: 'featured',
      title: '推荐',
      render: (value: boolean) => (
        <Badge variant={value ? 'default' : 'outline'}>
          {value ? '是' : '否'}
        </Badge>
      ),
    },
  ]

  // 订单表格列配置
  const orderColumns = [
    {
      key: 'orderNumber',
      title: '订单号',
      render: (value: string) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'user',
      title: '客户',
      render: (value: any) => (
        <div>
          <div className="font-medium">{value?.name || '未知用户'}</div>
          <div className="text-sm text-gray-500">{value?.email}</div>
        </div>
      ),
    },
    {
      key: 'totalAmount',
      title: '金额',
      render: (value: number) => `¥${value?.toFixed(2) || '0.00'}`,
    },
    {
      key: 'status',
      title: '订单状态',
      render: (value: string) => {
        const statusMap = {
          pending: { label: '待处理', variant: 'secondary' as const },
          confirmed: { label: '已确认', variant: 'default' as const },
          shipped: { label: '已发货', variant: 'default' as const },
          delivered: { label: '已送达', variant: 'default' as const },
          completed: { label: '已完成', variant: 'default' as const },
          cancelled: { label: '已取消', variant: 'destructive' as const },
        }
        const status = statusMap[value as keyof typeof statusMap] || { label: value, variant: 'outline' as const }
        return <Badge variant={status.variant}>{status.label}</Badge>
      },
    },
    {
      key: 'paymentStatus',
      title: '支付状态',
      render: (value: string) => {
        const statusMap = {
          pending: { label: '待支付', variant: 'secondary' as const },
          paid: { label: '已支付', variant: 'default' as const },
          failed: { label: '支付失败', variant: 'destructive' as const },
          refunded: { label: '已退款', variant: 'outline' as const },
        }
        const status = statusMap[value as keyof typeof statusMap] || { label: value, variant: 'outline' as const }
        return <Badge variant={status.variant}>{status.label}</Badge>
      },
    },
    {
      key: 'createdAt',
      title: '下单时间',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ]

  // 公司表格列配置
  const companyColumns = [
    {
      key: 'name',
      title: '公司名称',
      render: (value: string, record: any) => (
        <div className="flex items-center space-x-2">
          {record.logo && (
            <img src={record.logo} alt={value} className="w-8 h-8 rounded" />
          )}
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-gray-500">{record.website}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'industry',
      title: '行业',
      render: (value: string) => value || '未分类',
    },
    {
      key: 'size',
      title: '规模',
      render: (value: string) => {
        const sizeMap = {
          small: '小型 (1-50人)',
          medium: '中型 (51-200人)',
          large: '大型 (201-1000人)',
          enterprise: '企业级 (1000+人)',
        }
        return sizeMap[value as keyof typeof sizeMap] || value
      },
    },
    {
      key: 'verified',
      title: '认证状态',
      render: (value: boolean) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? '已认证' : '未认证'}
        </Badge>
      ),
    },
    {
      key: 'contactEmail',
      title: '联系邮箱',
      render: (value: string) => value || '未设置',
    },
    {
      key: 'contactPerson',
      title: '联系人',
      render: (value: string) => value || '未设置',
    },
    {
      key: 'createdAt',
      title: '注册时间',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ]

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData?.overview.users.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              新增 {dashboardData?.overview.users.new || 0} 人
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总产品数</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData?.overview.products.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              已发布 {dashboardData?.overview.products.published || 0} 个
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总订单数</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData?.overview.orders.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              待处理 {dashboardData?.overview.orders.pending || 0} 个
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ¥{dashboardData?.overview.revenue.total?.toFixed(2) || '0.00'}
            </div>
            <p className="text-xs text-muted-foreground">
              本期 ¥{dashboardData?.overview.revenue.period?.toFixed(2) || '0.00'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 系统状态 */}
      <Card>
        <CardHeader>
          <CardTitle>系统状态</CardTitle>
          <CardDescription>各项服务运行状态</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(dashboardData?.systemStatus || {}).map(([key, status]) => (
              <div key={key} className="flex items-center space-x-2">
                {status === 'healthy' ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm capitalize">{key}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 最近活动 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>最新用户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.recentActivity.users.slice(0, 5).map((user: any) => (
                <div key={user.id} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">
                      {user.name?.charAt(0) || user.email?.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{user.name || user.email}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>最新订单</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.recentActivity.orders.slice(0, 5).map((order: any) => (
                <div key={order.id} className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium">{order.orderNumber}</div>
                    <div className="text-xs text-gray-500">
                      {order.user?.name || '未知用户'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">¥{order.totalAmount?.toFixed(2)}</div>
                    <Badge variant="secondary" className="text-xs">
                      {order.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard()
      case 'users':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">用户管理</h2>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  新增用户
                </Button>
              </div>
            </div>
            <DataTable
              data={users}
              columns={userColumns}
              searchable
              selectable
              pagination
            />
          </div>
        )
      case 'products':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">产品管理</h2>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  新增产品
                </Button>
              </div>
            </div>
            <DataTable
              data={products}
              columns={productColumns}
              searchable
              selectable
              pagination
            />
          </div>
        )
      case 'orders':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">订单管理</h2>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  新增订单
                </Button>
              </div>
            </div>
            <DataTable
              data={orders}
              columns={orderColumns}
              searchable
              selectable
              pagination
            />
          </div>
        )
      default:
        return renderDashboard()
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 标签导航 */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'dashboard', label: '仪表板', icon: TrendingUp },
              { key: 'users', label: '用户管理', icon: Users },
              { key: 'products', label: '产品管理', icon: Package },
              { key: 'orders', label: '订单管理', icon: ShoppingCart },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* 内容区域 */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : (
          renderContent()
        )}
      </div>
    </AdminLayout>
  )
}
