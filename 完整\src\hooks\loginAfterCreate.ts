import type { CollectionAfterChangeHook } from 'payload'

/**
 * 用户创建后自动登录的钩子
 * 当新用户注册时，自动为其设置登录状态
 */
export const loginAfterCreate: CollectionAfterChangeHook = async ({
  doc,
  req,
  operation,
}) => {
  // 只在创建操作时执行
  if (operation === 'create') {
    const { payload, res } = req

    try {
      // 更新最后登录时间
      await payload.update({
        collection: 'users',
        id: doc.id,
        data: {
          lastLoginAt: new Date().toISOString(),
        },
      })

      // 设置用户登录状态
      await payload.login({
        collection: 'users',
        data: {
          email: doc.email,
          password: doc.password,
        },
        req,
        res,
      })

      // 记录登录日志
      await payload.create({
        collection: 'access-logs',
        data: {
          user: doc.id,
          action: 'register_and_login',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
          success: true,
        },
      })

      // 发送欢迎邮件
      if (doc.email) {
        // 这里可以集成邮件服务
        console.log(`发送欢迎邮件给: ${doc.email}`)
      }

      // 为新用户赠送积分
      if (doc.points === undefined || doc.points === 0) {
        await payload.update({
          collection: 'users',
          id: doc.id,
          data: {
            points: 100, // 新用户赠送100积分
          },
        })

        // 记录积分交易
        await payload.create({
          collection: 'points-transactions',
          data: {
            user: doc.id,
            type: 'reward',
            amount: 100,
            description: '新用户注册奖励',
            status: 'completed',
          },
        })
      }

    } catch (error) {
      console.error('登录后处理失败:', error)
      
      // 记录错误日志
      await payload.create({
        collection: 'access-logs',
        data: {
          user: doc.id,
          action: 'register_and_login',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        },
      })
    }
  }

  return doc
}
