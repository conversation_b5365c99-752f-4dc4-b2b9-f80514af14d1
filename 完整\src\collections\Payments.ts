import type { CollectionConfig } from 'payload'
import { admins, userOrders } from '../access/index'

export const Payments: CollectionConfig = {
  slug: 'payments',
  admin: {
    useAsTitle: 'transactionId',
    defaultColumns: ['transactionId', 'user', 'amount', 'method', 'status', 'createdAt'],
    listSearchableFields: ['transactionId', 'notes'],
    group: '财务管理',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role?.includes('admin')) return true
      
      return {
        user: {
          equals: user.id,
        },
      }
    },
    create: userOrders,
    update: admins,
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 生成交易ID
          if (!data.transactionId) {
            const timestamp = Date.now()
            const random = Math.random().toString(36).substr(2, 8).toUpperCase()
            data.transactionId = `PAY-${timestamp}-${random}`
          }
          
          // 设置默认状态
          data.status = data.status || 'pending'
          
          // 设置用户
          if (!data.user && req.user) {
            data.user = req.user.id
          }
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'transactionId',
      label: '交易ID',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'user',
      label: '用户',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'order',
      label: '关联订单',
      type: 'relationship',
      relationTo: 'orders',
      admin: {
        description: '如果是订单支付，选择对应订单',
      },
    },
    {
      name: 'subscription',
      label: '关联订阅',
      type: 'relationship',
      relationTo: 'subscriptions',
      admin: {
        description: '如果是订阅支付，选择对应订阅',
      },
    },
    {
      name: 'type',
      label: '支付类型',
      type: 'select',
      required: true,
      options: [
        { label: '订单支付', value: 'order' },
        { label: 'VIP订阅', value: 'subscription' },
        { label: '积分充值', value: 'points' },
        { label: '保证金', value: 'deposit' },
        { label: '退款', value: 'refund' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'amount',
      label: '金额',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'currency',
      label: '货币',
      type: 'select',
      defaultValue: 'CNY',
      options: [
        { label: '人民币 (CNY)', value: 'CNY' },
        { label: '美元 (USD)', value: 'USD' },
        { label: '欧元 (EUR)', value: 'EUR' },
      ],
    },
    {
      name: 'method',
      label: '支付方式',
      type: 'select',
      required: true,
      options: [
        { label: '支付宝', value: 'alipay' },
        { label: '微信支付', value: 'wechat' },
        { label: 'PayPal', value: 'paypal' },
        { label: '银行转账', value: 'bank_transfer' },
        { label: '信用卡', value: 'credit_card' },
        { label: '现金', value: 'cash' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'status',
      label: '支付状态',
      type: 'select',
      defaultValue: 'pending',
      options: [
        { label: '待支付', value: 'pending' },
        { label: '支付中', value: 'processing' },
        { label: '支付成功', value: 'completed' },
        { label: '支付失败', value: 'failed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已退款', value: 'refunded' },
      ],
    },
    {
      name: 'gatewayInfo',
      label: '支付网关信息',
      type: 'group',
      fields: [
        {
          name: 'gateway',
          label: '支付网关',
          type: 'text',
        },
        {
          name: 'gatewayTransactionId',
          label: '网关交易ID',
          type: 'text',
        },
        {
          name: 'gatewayResponse',
          label: '网关响应',
          type: 'json',
        },
        {
          name: 'gatewayFee',
          label: '网关手续费',
          type: 'number',
          min: 0,
        },
      ],
    },
    {
      name: 'timeline',
      label: '支付时间线',
      type: 'array',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'status',
          label: '状态',
          type: 'text',
        },
        {
          name: 'timestamp',
          label: '时间',
          type: 'date',
        },
        {
          name: 'notes',
          label: '备注',
          type: 'text',
        },
        {
          name: 'operator',
          label: '操作人',
          type: 'relationship',
          relationTo: 'users',
        },
      ],
    },
    {
      name: 'refundInfo',
      label: '退款信息',
      type: 'group',
      admin: {
        condition: (data) => data.status === 'refunded',
      },
      fields: [
        {
          name: 'refundAmount',
          label: '退款金额',
          type: 'number',
          min: 0,
        },
        {
          name: 'refundReason',
          label: '退款原因',
          type: 'textarea',
        },
        {
          name: 'refundDate',
          label: '退款日期',
          type: 'date',
        },
        {
          name: 'refundTransactionId',
          label: '退款交易ID',
          type: 'text',
        },
      ],
    },
    {
      name: 'notes',
      label: '备注',
      type: 'textarea',
    },
    {
      name: 'internalNotes',
      label: '内部备注',
      type: 'textarea',
      access: {
        read: admins,
        update: admins,
      },
    },
    {
      name: 'attachments',
      label: '附件',
      type: 'array',
      fields: [
        {
          name: 'file',
          label: '文件',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'description',
          label: '描述',
          type: 'text',
        },
      ],
    },
    {
      name: 'metadata',
      label: '元数据',
      type: 'json',
      admin: {
        description: '存储额外的支付相关数据',
      },
    },
  ],
  timestamps: true,
}
