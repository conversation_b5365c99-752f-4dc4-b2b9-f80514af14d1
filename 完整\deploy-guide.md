# B2B贸易平台部署指南

## 宝塔面板部署步骤

### 1. 环境准备

#### 1.1 安装Node.js
```bash
# 在宝塔面板中安装Node.js 18.x
# 软件商店 -> 运行环境 -> Node.js -> 安装
```

#### 1.2 安装MongoDB
```bash
# 方式一：通过宝塔面板安装
# 软件商店 -> 数据库 -> MongoDB -> 安装

# 方式二：手动安装
wget https://fastdl.mongodb.org/linux/mongodb-linux-x86_64-rhel70-4.4.18.tgz
tar -zxvf mongodb-linux-x86_64-rhel70-4.4.18.tgz
mv mongodb-linux-x86_64-rhel70-4.4.18 /usr/local/mongodb
```

#### 1.3 安装PM2
```bash
npm install -g pm2
```

### 2. 项目部署

#### 2.1 上传项目文件
```bash
# 将项目文件上传到服务器
# 建议路径: /www/wwwroot/b2b-platform/
```

#### 2.2 安装依赖
```bash
cd /www/wwwroot/b2b-platform/完整
npm install --production
```

#### 2.3 配置环境变量
```bash
# 复制环境变量文件
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

重要配置项：
```env
# 生产环境配置
NODE_ENV=production
NEXT_PUBLIC_SERVER_URL=https://yourdomain.com
PAYLOAD_PUBLIC_SERVER_URL=https://yourdomain.com

# 数据库配置
DATABASE_URI=mongodb://localhost:27017/b2b-trade-platform

# 安全密钥（必须修改）
PAYLOAD_SECRET=your-super-secret-key-minimum-32-characters
JWT_SECRET=your-jwt-secret-key-minimum-32-characters

# 支付配置
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_ENVIRONMENT=live

# 邮件配置
SMTP_HOST=smtp.your-email-provider.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

#### 2.4 构建项目
```bash
npm run build
```

#### 2.5 启动应用
```bash
# 使用PM2启动
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 3. Nginx配置

#### 3.1 创建站点
在宝塔面板中：
1. 网站 -> 添加站点
2. 域名：yourdomain.com
3. 根目录：/www/wwwroot/b2b-platform/完整

#### 3.2 配置反向代理
```nginx
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    proxy_read_timeout 86400;
}

# 静态文件缓存
location /_next/static {
    alias /www/wwwroot/b2b-platform/完整/.next/static;
    expires 365d;
    access_log off;
}

location /public {
    alias /www/wwwroot/b2b-platform/完整/public;
    expires 30d;
    access_log off;
}
```

#### 3.3 SSL证书配置
1. 在宝塔面板中申请Let's Encrypt证书
2. 或上传自己的SSL证书
3. 强制HTTPS重定向

### 4. 数据库配置

#### 4.1 MongoDB配置
```bash
# 启动MongoDB服务
systemctl start mongod
systemctl enable mongod

# 创建数据库用户
mongo
use b2b-trade-platform
db.createUser({
  user: "b2b_user",
  pwd: "your_password",
  roles: ["readWrite"]
})
```

#### 4.2 数据库连接
更新 `.env.local` 中的数据库连接：
```env
DATABASE_URI=*******************************************************************
```

### 5. 安全配置

#### 5.1 防火墙设置
```bash
# 开放必要端口
ufw allow 80
ufw allow 443
ufw allow 22

# 关闭不必要的端口
ufw deny 3000  # Node.js端口只允许内部访问
```

#### 5.2 文件权限
```bash
# 设置项目文件权限
chown -R www:www /www/wwwroot/b2b-platform/
chmod -R 755 /www/wwwroot/b2b-platform/
chmod 600 /www/wwwroot/b2b-platform/完整/.env.local
```

### 6. 监控和日志

#### 6.1 PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs b2b-trade-platform

# 重启应用
pm2 restart b2b-trade-platform

# 查看详细信息
pm2 show b2b-trade-platform
```

#### 6.2 日志配置
创建日志目录：
```bash
mkdir -p /www/wwwroot/b2b-platform/logs
chown www:www /www/wwwroot/b2b-platform/logs
```

#### 6.3 日志轮转
创建 `/etc/logrotate.d/b2b-platform`：
```
/www/wwwroot/b2b-platform/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 7. 备份策略

#### 7.1 数据库备份
创建备份脚本 `/www/backup/mongodb-backup.sh`：
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/mongodb"
mkdir -p $BACKUP_DIR

mongodump --db b2b-trade-platform --out $BACKUP_DIR/backup_$DATE

# 保留最近30天的备份
find $BACKUP_DIR -type d -name "backup_*" -mtime +30 -exec rm -rf {} \;
```

#### 7.2 文件备份
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/files"
mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/b2b-platform_$DATE.tar.gz /www/wwwroot/b2b-platform/

# 保留最近30天的备份
find $BACKUP_DIR -name "b2b-platform_*.tar.gz" -mtime +30 -delete
```

#### 7.3 定时备份
添加到crontab：
```bash
# 每天凌晨2点备份数据库
0 2 * * * /www/backup/mongodb-backup.sh

# 每周日凌晨3点备份文件
0 3 * * 0 /www/backup/files-backup.sh
```

### 8. 性能优化

#### 8.1 Node.js优化
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
```

#### 8.2 Nginx优化
```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 设置缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 9. 故障排除

#### 9.1 常见问题

**问题1：应用无法启动**
```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 检查PM2日志
pm2 logs b2b-trade-platform

# 检查环境变量
pm2 env b2b-trade-platform
```

**问题2：数据库连接失败**
```bash
# 检查MongoDB状态
systemctl status mongod

# 测试数据库连接
mongo --eval "db.adminCommand('ismaster')"
```

**问题3：文件上传失败**
```bash
# 检查文件权限
ls -la /www/wwwroot/b2b-platform/完整/public/uploads/

# 检查磁盘空间
df -h
```

#### 9.2 性能监控
```bash
# 查看系统资源使用
htop

# 查看Node.js进程
pm2 monit

# 查看数据库性能
mongostat
```

### 10. 更新部署

#### 10.1 代码更新
```bash
# 拉取最新代码
cd /www/wwwroot/b2b-platform/完整
git pull origin main

# 安装新依赖
npm install --production

# 重新构建
npm run build

# 重启应用
pm2 restart b2b-trade-platform
```

#### 10.2 零停机更新
```bash
# 使用PM2的reload功能
pm2 reload b2b-trade-platform
```

### 11. 安全检查清单

- [ ] 修改默认密钥和密码
- [ ] 配置防火墙规则
- [ ] 启用SSL证书
- [ ] 设置正确的文件权限
- [ ] 配置访问日志
- [ ] 启用速率限制
- [ ] 定期更新系统和依赖
- [ ] 配置自动备份
- [ ] 设置监控告警

### 12. 联系支持

如果在部署过程中遇到问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：+86 400-123-4567
- 在线文档：https://docs.b2b-platform.com
