import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../payload.config'

// 获取仪表板统计数据
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // 默认30天

    const now = new Date()
    const periodDays = parseInt(period)
    const startDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000)

    // 用户统计
    const totalUsers = await payload.count({
      collection: 'users',
      where: {},
    })

    const newUsers = await payload.count({
      collection: 'users',
      where: {
        createdAt: { greater_than: startDate },
      },
    })

    const activeUsers = await payload.count({
      collection: 'users',
      where: { status: { equals: 'active' } },
    })

    const vipUsers = await payload.count({
      collection: 'users',
      where: { vipLevel: { not_equals: 'basic' } },
    })

    // 产品统计
    const totalProducts = await payload.count({
      collection: 'products',
      where: {},
    })

    const publishedProducts = await payload.count({
      collection: 'products',
      where: { status: { equals: 'published' } },
    })

    const featuredProducts = await payload.count({
      collection: 'products',
      where: { featured: { equals: true } },
    })

    const lowStockProducts = await payload.count({
      collection: 'products',
      where: { 
        and: [
          { inventory: { less_than: 10 } },
          { status: { equals: 'published' } }
        ]
      },
    })

    // 订单统计
    const totalOrders = await payload.count({
      collection: 'orders',
      where: {},
    })

    const newOrders = await payload.count({
      collection: 'orders',
      where: {
        createdAt: { greater_than: startDate },
      },
    })

    const pendingOrders = await payload.count({
      collection: 'orders',
      where: { status: { equals: 'pending' } },
    })

    const completedOrders = await payload.count({
      collection: 'orders',
      where: { status: { equals: 'completed' } },
    })

    // 收入统计
    const revenueOrders = await payload.find({
      collection: 'orders',
      where: { 
        and: [
          { status: { equals: 'completed' } },
          { paymentStatus: { equals: 'paid' } }
        ]
      },
      limit: 1000,
    })

    const totalRevenue = revenueOrders.docs.reduce((sum, order) => sum + (order.totalAmount || 0), 0)

    const periodRevenueOrders = await payload.find({
      collection: 'orders',
      where: { 
        and: [
          { status: { equals: 'completed' } },
          { paymentStatus: { equals: 'paid' } },
          { createdAt: { greater_than: startDate } }
        ]
      },
      limit: 1000,
    })

    const periodRevenue = periodRevenueOrders.docs.reduce((sum, order) => sum + (order.totalAmount || 0), 0)

    // 支付统计
    const totalPayments = await payload.count({
      collection: 'payments',
      where: {},
    })

    const successfulPayments = await payload.count({
      collection: 'payments',
      where: { status: { equals: 'completed' } },
    })

    const failedPayments = await payload.count({
      collection: 'payments',
      where: { status: { equals: 'failed' } },
    })

    // 订阅统计
    const totalSubscriptions = await payload.count({
      collection: 'subscriptions',
      where: {},
    })

    const activeSubscriptions = await payload.count({
      collection: 'subscriptions',
      where: { status: { equals: 'active' } },
    })

    // 企业统计
    const totalCompanies = await payload.count({
      collection: 'companies',
      where: {},
    })

    const verifiedCompanies = await payload.count({
      collection: 'companies',
      where: { verified: { equals: true } },
    })

    // 最近活动
    const recentUsers = await payload.find({
      collection: 'users',
      limit: 5,
      sort: '-createdAt',
      depth: 1,
    })

    const recentOrders = await payload.find({
      collection: 'orders',
      limit: 5,
      sort: '-createdAt',
      depth: 2,
    })

    const recentPayments = await payload.find({
      collection: 'payments',
      limit: 5,
      sort: '-createdAt',
      depth: 2,
    })

    // 系统状态
    const systemStatus = {
      database: 'healthy',
      storage: 'healthy',
      payment: 'healthy',
      email: 'healthy',
    }

    // 趋势数据（简化版，实际应该按天分组）
    const dailyStats = []
    for (let i = periodDays - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000)
      
      const dayUsers = await payload.count({
        collection: 'users',
        where: {
          and: [
            { createdAt: { greater_than_equal: date } },
            { createdAt: { less_than: nextDate } }
          ]
        },
      })

      const dayOrders = await payload.count({
        collection: 'orders',
        where: {
          and: [
            { createdAt: { greater_than_equal: date } },
            { createdAt: { less_than: nextDate } }
          ]
        },
      })

      const dayRevenue = await payload.find({
        collection: 'orders',
        where: {
          and: [
            { status: { equals: 'completed' } },
            { paymentStatus: { equals: 'paid' } },
            { createdAt: { greater_than_equal: date } },
            { createdAt: { less_than: nextDate } }
          ]
        },
        limit: 100,
      })

      const revenue = dayRevenue.docs.reduce((sum, order) => sum + (order.totalAmount || 0), 0)

      dailyStats.push({
        date: date.toISOString().split('T')[0],
        users: dayUsers.totalDocs,
        orders: dayOrders.totalDocs,
        revenue,
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          users: {
            total: totalUsers.totalDocs,
            new: newUsers.totalDocs,
            active: activeUsers.totalDocs,
            vip: vipUsers.totalDocs,
          },
          products: {
            total: totalProducts.totalDocs,
            published: publishedProducts.totalDocs,
            featured: featuredProducts.totalDocs,
            lowStock: lowStockProducts.totalDocs,
          },
          orders: {
            total: totalOrders.totalDocs,
            new: newOrders.totalDocs,
            pending: pendingOrders.totalDocs,
            completed: completedOrders.totalDocs,
          },
          revenue: {
            total: totalRevenue,
            period: periodRevenue,
            currency: 'CNY',
          },
          payments: {
            total: totalPayments.totalDocs,
            successful: successfulPayments.totalDocs,
            failed: failedPayments.totalDocs,
            successRate: totalPayments.totalDocs > 0 
              ? Math.round((successfulPayments.totalDocs / totalPayments.totalDocs) * 100) 
              : 0,
          },
          subscriptions: {
            total: totalSubscriptions.totalDocs,
            active: activeSubscriptions.totalDocs,
          },
          companies: {
            total: totalCompanies.totalDocs,
            verified: verifiedCompanies.totalDocs,
          },
        },
        recentActivity: {
          users: recentUsers.docs,
          orders: recentOrders.docs,
          payments: recentPayments.docs,
        },
        systemStatus,
        trends: dailyStats,
        period: periodDays,
        generatedAt: new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    return NextResponse.json(
      { success: false, error: '获取仪表板数据失败' },
      { status: 500 }
    )
  }
}

// 获取实时统计数据
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { metrics } = body

    const result: any = {}

    if (metrics.includes('onlineUsers')) {
      // 获取最近5分钟活跃的用户
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      const onlineUsers = await payload.count({
        collection: 'accessLogs',
        where: {
          createdAt: { greater_than: fiveMinutesAgo },
        },
      })
      result.onlineUsers = onlineUsers.totalDocs
    }

    if (metrics.includes('todayOrders')) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayOrders = await payload.count({
        collection: 'orders',
        where: {
          createdAt: { greater_than: today },
        },
      })
      result.todayOrders = todayOrders.totalDocs
    }

    if (metrics.includes('todayRevenue')) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayOrders = await payload.find({
        collection: 'orders',
        where: {
          and: [
            { status: { equals: 'completed' } },
            { paymentStatus: { equals: 'paid' } },
            { createdAt: { greater_than: today } }
          ]
        },
        limit: 1000,
      })
      const todayRevenue = todayOrders.docs.reduce((sum, order) => sum + (order.totalAmount || 0), 0)
      result.todayRevenue = todayRevenue
    }

    if (metrics.includes('pendingOrders')) {
      const pendingOrders = await payload.count({
        collection: 'orders',
        where: { status: { equals: 'pending' } },
      })
      result.pendingOrders = pendingOrders.totalDocs
    }

    if (metrics.includes('lowStockProducts')) {
      const lowStockProducts = await payload.count({
        collection: 'products',
        where: { 
          and: [
            { inventory: { less_than: 10 } },
            { status: { equals: 'published' } }
          ]
        },
      })
      result.lowStockProducts = lowStockProducts.totalDocs
    }

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('获取实时数据失败:', error)
    return NextResponse.json(
      { success: false, error: '获取实时数据失败' },
      { status: 500 }
    )
  }
}
