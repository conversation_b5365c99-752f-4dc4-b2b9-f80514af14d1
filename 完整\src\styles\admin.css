/* 管理后台自定义样式 */

/* 基础变量 */
:root {
  --admin-primary: #3b82f6;
  --admin-primary-dark: #2563eb;
  --admin-secondary: #64748b;
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-error: #ef4444;
  --admin-background: #f8fafc;
  --admin-surface: #ffffff;
  --admin-border: #e2e8f0;
  --admin-text: #1e293b;
  --admin-text-muted: #64748b;
  --admin-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --admin-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* 暗色主题 */
[data-theme="dark"] {
  --admin-primary: #60a5fa;
  --admin-primary-dark: #3b82f6;
  --admin-secondary: #94a3b8;
  --admin-success: #34d399;
  --admin-warning: #fbbf24;
  --admin-error: #f87171;
  --admin-background: #0f172a;
  --admin-surface: #1e293b;
  --admin-border: #334155;
  --admin-text: #f1f5f9;
  --admin-text-muted: #94a3b8;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--admin-background);
  color: var(--admin-text);
}

/* Payload CMS 管理界面自定义 */
.payload-admin {
  --color-base-0: var(--admin-surface);
  --color-base-50: var(--admin-background);
  --color-base-100: #f1f5f9;
  --color-base-200: #e2e8f0;
  --color-base-300: #cbd5e1;
  --color-base-400: #94a3b8;
  --color-base-500: var(--admin-secondary);
  --color-base-600: #475569;
  --color-base-700: #334155;
  --color-base-800: #1e293b;
  --color-base-900: #0f172a;
  
  --color-text: var(--admin-text);
  --color-text-dim: var(--admin-text-muted);
  
  --color-success-50: #ecfdf5;
  --color-success-500: var(--admin-success);
  --color-success-600: #059669;
  
  --color-warning-50: #fffbeb;
  --color-warning-500: var(--admin-warning);
  --color-warning-600: #d97706;
  
  --color-error-50: #fef2f2;
  --color-error-500: var(--admin-error);
  --color-error-600: #dc2626;
  
  --color-elevation-50: var(--admin-surface);
  --color-elevation-100: var(--admin-surface);
  --color-elevation-200: var(--admin-surface);
  --color-elevation-300: var(--admin-surface);
  --color-elevation-400: var(--admin-surface);
  --color-elevation-500: var(--admin-surface);
  
  --theme-bg: var(--admin-background);
  --theme-input-bg: var(--admin-surface);
  --theme-elevation-50: var(--admin-surface);
  --theme-elevation-100: var(--admin-surface);
  --theme-elevation-200: var(--admin-surface);
  --theme-elevation-300: var(--admin-surface);
  --theme-elevation-400: var(--admin-surface);
  --theme-elevation-500: var(--admin-surface);
  
  --font-body: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  --font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas,
    'Courier New', monospace;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--admin-background);
}

::-webkit-scrollbar-thumb {
  background: var(--admin-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--admin-secondary);
}

/* 管理后台导航样式 */
.admin-nav {
  background: var(--admin-surface);
  border-right: 1px solid var(--admin-border);
  box-shadow: var(--admin-shadow);
}

.admin-nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--admin-text-muted);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  margin: 0.25rem 0.5rem;
}

.admin-nav-item:hover {
  background: var(--admin-background);
  color: var(--admin-text);
}

.admin-nav-item.active {
  background: var(--admin-primary);
  color: white;
}

.admin-nav-item svg {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
}

/* 管理后台卡片样式 */
.admin-card {
  background: var(--admin-surface);
  border: 1px solid var(--admin-border);
  border-radius: 0.75rem;
  box-shadow: var(--admin-shadow);
  overflow: hidden;
  transition: all 0.2s ease;
}

.admin-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-1px);
}

.admin-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--admin-border);
}

.admin-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text);
  margin: 0;
}

.admin-card-description {
  font-size: 0.875rem;
  color: var(--admin-text-muted);
  margin: 0.25rem 0 0 0;
}

.admin-card-content {
  padding: 1.5rem;
}

/* 管理后台按钮样式 */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover {
  background: var(--admin-primary-dark);
}

.admin-btn-secondary {
  background: var(--admin-surface);
  color: var(--admin-text);
  border: 1px solid var(--admin-border);
}

.admin-btn-secondary:hover {
  background: var(--admin-background);
}

.admin-btn-success {
  background: var(--admin-success);
  color: white;
}

.admin-btn-warning {
  background: var(--admin-warning);
  color: white;
}

.admin-btn-error {
  background: var(--admin-error);
  color: white;
}

/* 管理后台表格样式 */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-surface);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: var(--admin-shadow);
}

.admin-table th {
  background: var(--admin-background);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--admin-text);
  border-bottom: 1px solid var(--admin-border);
}

.admin-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--admin-border);
  color: var(--admin-text);
}

.admin-table tr:hover {
  background: var(--admin-background);
}

/* 管理后台表单样式 */
.admin-form-group {
  margin-bottom: 1.5rem;
}

.admin-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--admin-text);
  margin-bottom: 0.5rem;
}

.admin-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--admin-border);
  border-radius: 0.5rem;
  background: var(--admin-surface);
  color: var(--admin-text);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.admin-form-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* 管理后台徽章样式 */
.admin-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.admin-badge-success {
  background: rgb(34 197 94 / 0.1);
  color: var(--admin-success);
}

.admin-badge-warning {
  background: rgb(245 158 11 / 0.1);
  color: var(--admin-warning);
}

.admin-badge-error {
  background: rgb(239 68 68 / 0.1);
  color: var(--admin-error);
}

.admin-badge-info {
  background: rgb(59 130 246 / 0.1);
  color: var(--admin-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-card {
    margin: 0.5rem;
  }
  
  .admin-card-header,
  .admin-card-content {
    padding: 1rem;
  }
  
  .admin-table {
    font-size: 0.875rem;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 0.75rem 0.5rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.admin-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 加载状态 */
.admin-loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--admin-border);
  border-radius: 50%;
  border-top-color: var(--admin-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
