import type { CollectionConfig } from 'payload'
import { admins, loggedIn, anyone } from '../access/index'

export const Media: CollectionConfig = {
  slug: 'media',
  upload: {
    staticDir: 'uploads',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 150,
        height: 150,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 80,
          },
        },
      },
      {
        name: 'small',
        width: 300,
        height: 300,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
      {
        name: 'medium',
        width: 600,
        height: 600,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
      {
        name: 'large',
        width: 1200,
        height: 1200,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
      {
        name: 'xlarge',
        width: 1920,
        height: 1920,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'image/svg+xml',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'video/mp4',
      'video/webm',
      'audio/mpeg',
      'audio/wav',
    ],
  },
  admin: {
    useAsTitle: 'filename',
    defaultColumns: ['filename', 'alt', 'filesize', 'mimeType', 'createdAt'],
    group: '媒体管理',
  },
  access: {
    read: anyone,
    create: loggedIn,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      // 管理员可以更新所有媒体
      if (user.role?.includes('admin')) return true
      
      // 用户只能更新自己上传的媒体
      return {
        uploadedBy: {
          equals: user.id,
        },
      }
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      
      // 管理员可以删除所有媒体
      if (user.role?.includes('admin')) return true
      
      // 用户只能删除自己上传的媒体
      return {
        uploadedBy: {
          equals: user.id,
        },
      }
    },
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create' && req.user) {
          data.uploadedBy = req.user.id
          data.uploadedAt = new Date().toISOString()
        }
        return data
      },
    ],
    afterDelete: [
      ({ doc }) => {
        // 清理相关的文件系统文件
        console.log(`媒体文件已删除: ${doc.filename}`)
      },
    ],
  },
  fields: [
    {
      name: 'alt',
      label: '替代文本',
      type: 'text',
      admin: {
        description: '图片的替代文本，用于SEO和无障碍访问',
      },
    },
    {
      name: 'caption',
      label: '图片说明',
      type: 'text',
      admin: {
        description: '图片的详细说明',
      },
    },
    {
      name: 'uploadedBy',
      label: '上传者',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'uploadedAt',
      label: '上传时间',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'category',
      label: '媒体分类',
      type: 'select',
      options: [
        { label: '产品图片', value: 'product' },
        { label: '用户头像', value: 'avatar' },
        { label: '公司logo', value: 'logo' },
        { label: '证书文件', value: 'certificate' },
        { label: '营业执照', value: 'license' },
        { label: '宣传资料', value: 'marketing' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'tags',
      label: '标签',
      type: 'text',
      hasMany: true,
      admin: {
        description: '用于搜索和分类的标签',
      },
    },
    {
      name: 'isPublic',
      label: '公开访问',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: '是否允许公开访问此文件',
      },
    },
    {
      name: 'watermark',
      label: '添加水印',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: '是否为图片添加水印',
        condition: (data) => {
          return data.mimeType?.startsWith('image/')
        },
      },
    },
    {
      name: 'compressionLevel',
      label: '压缩级别',
      type: 'select',
      defaultValue: 'medium',
      options: [
        { label: '低压缩（高质量）', value: 'low' },
        { label: '中等压缩', value: 'medium' },
        { label: '高压缩（小文件）', value: 'high' },
      ],
      admin: {
        condition: (data) => {
          return data.mimeType?.startsWith('image/')
        },
      },
    },
    {
      name: 'exifData',
      label: 'EXIF数据',
      type: 'json',
      admin: {
        readOnly: true,
        description: '图片的EXIF元数据',
        condition: (data) => {
          return data.mimeType?.startsWith('image/')
        },
      },
    },
    {
      name: 'usage',
      label: '使用情况',
      type: 'array',
      admin: {
        readOnly: true,
        description: '此媒体文件的使用记录',
      },
      fields: [
        {
          name: 'collection',
          label: '集合',
          type: 'text',
        },
        {
          name: 'id',
          label: '文档ID',
          type: 'text',
        },
        {
          name: 'field',
          label: '字段',
          type: 'text',
        },
      ],
    },
    {
      name: 'downloadCount',
      label: '下载次数',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'lastDownloadAt',
      label: '最后下载时间',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'metadata',
      label: '元数据',
      type: 'json',
      admin: {
        description: '存储额外的文件元数据',
      },
    },
  ],
  timestamps: true,
}
