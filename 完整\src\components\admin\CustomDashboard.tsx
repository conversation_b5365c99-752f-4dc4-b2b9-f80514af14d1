'use client'

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Package, 
  ShoppingCart, 
  CreditCard, 
  TrendingUp, 
  TrendingDown,
  Eye,
  UserCheck,
  Building,
  Star
} from 'lucide-react'

interface DashboardStats {
  users: {
    total: number
    new: number
    active: number
    vip: number
  }
  products: {
    total: number
    published: number
    pending: number
    views: number
  }
  orders: {
    total: number
    pending: number
    completed: number
    revenue: number
  }
  companies: {
    total: number
    verified: number
    pending: number
  }
}

export default function CustomDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟数据加载
    const loadStats = async () => {
      try {
        // 这里应该调用实际的API
        const mockStats: DashboardStats = {
          users: {
            total: 1248,
            new: 23,
            active: 892,
            vip: 156
          },
          products: {
            total: 3456,
            published: 3201,
            pending: 255,
            views: 45678
          },
          orders: {
            total: 789,
            pending: 45,
            completed: 698,
            revenue: 234567.89
          },
          companies: {
            total: 234,
            verified: 189,
            pending: 45
          }
        }
        
        setTimeout(() => {
          setStats(mockStats)
          setLoading(false)
        }, 1000)
      } catch (error) {
        console.error('Failed to load dashboard stats:', error)
        setLoading(false)
      }
    }

    loadStats()
  }, [])

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          无法加载仪表板数据
        </div>
      </div>
    )
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    trend = 'up',
    description 
  }: {
    title: string
    value: string | number
    change?: string
    icon: React.ElementType
    trend?: 'up' | 'down'
    description?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-gray-400" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        {change && (
          <div className="flex items-center space-x-1 text-xs">
            {trend === 'up' ? (
              <TrendingUp className="h-3 w-3 text-green-500" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-500" />
            )}
            <span className={trend === 'up' ? 'text-green-600' : 'text-red-600'}>
              {change}
            </span>
            <span className="text-gray-500">较上月</span>
          </div>
        )}
        {description && (
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="p-6 space-y-6">
      {/* 欢迎信息 */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">欢迎回来！</h1>
        <p className="text-blue-100">
          今天是 {new Date().toLocaleDateString('zh-CN', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
          })}
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总用户数"
          value={stats.users.total.toLocaleString()}
          change="+12.5%"
          icon={Users}
          description={`活跃用户: ${stats.users.active}`}
        />
        
        <StatCard
          title="总产品数"
          value={stats.products.total.toLocaleString()}
          change="+8.2%"
          icon={Package}
          description={`待审核: ${stats.products.pending}`}
        />
        
        <StatCard
          title="总订单数"
          value={stats.orders.total.toLocaleString()}
          change="+15.3%"
          icon={ShoppingCart}
          description={`待处理: ${stats.orders.pending}`}
        />
        
        <StatCard
          title="总收入"
          value={`¥${stats.orders.revenue.toLocaleString()}`}
          change="+23.1%"
          icon={CreditCard}
          description="本月收入"
        />
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="新用户"
          value={stats.users.new}
          change="+5.2%"
          icon={UserCheck}
          description="今日新增"
        />
        
        <StatCard
          title="VIP用户"
          value={stats.users.vip}
          change="+18.7%"
          icon={Star}
          description="付费用户"
        />
        
        <StatCard
          title="认证企业"
          value={stats.companies.verified}
          change="+7.4%"
          icon={Building}
          description={`待认证: ${stats.companies.pending}`}
        />
        
        <StatCard
          title="产品浏览"
          value={stats.products.views.toLocaleString()}
          change="+31.2%"
          icon={Eye}
          description="今日浏览量"
        />
      </div>

      {/* 快捷操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快捷操作</CardTitle>
          <CardDescription>
            常用的管理操作
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Users className="h-6 w-6" />
              <span>用户管理</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Package className="h-6 w-6" />
              <span>产品管理</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <ShoppingCart className="h-6 w-6" />
              <span>订单管理</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Building className="h-6 w-6" />
              <span>企业管理</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 系统状态 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>系统状态</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span>数据库连接</span>
              <Badge variant="default" className="bg-green-100 text-green-800">
                正常
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>文件存储</span>
              <Badge variant="default" className="bg-green-100 text-green-800">
                正常
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>支付服务</span>
              <Badge variant="default" className="bg-green-100 text-green-800">
                正常
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>邮件服务</span>
              <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                警告
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span>新用户注册</span>
                <span className="text-gray-500">2分钟前</span>
              </div>
              <div className="flex justify-between">
                <span>新订单创建</span>
                <span className="text-gray-500">5分钟前</span>
              </div>
              <div className="flex justify-between">
                <span>产品审核通过</span>
                <span className="text-gray-500">10分钟前</span>
              </div>
              <div className="flex justify-between">
                <span>企业认证申请</span>
                <span className="text-gray-500">15分钟前</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
