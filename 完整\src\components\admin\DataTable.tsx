'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { 
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react'

interface Column {
  key: string
  label: string
  sortable?: boolean
  width?: string
  render?: (value: any, row: any) => React.ReactNode
}

interface DataTableProps {
  title?: string
  columns: Column[]
  data: any[]
  loading?: boolean
  pagination?: {
    page: number
    pageSize: number
    total: number
    onPageChange: (page: number) => void
    onPageSizeChange: (pageSize: number) => void
  }
  sorting?: {
    field: string
    direction: 'asc' | 'desc'
    onSort: (field: string, direction: 'asc' | 'desc') => void
  }
  filters?: {
    search: string
    onSearchChange: (search: string) => void
  }
  actions?: {
    onView?: (row: any) => void
    onEdit?: (row: any) => void
    onDelete?: (row: any) => void
    custom?: Array<{
      label: string
      icon?: React.ElementType
      onClick: (row: any) => void
      variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    }>
  }
  selectable?: boolean
  onSelectionChange?: (selectedRows: any[]) => void
}

export default function DataTable({
  title,
  columns,
  data,
  loading = false,
  pagination,
  sorting,
  filters,
  actions,
  selectable = false,
  onSelectionChange
}: DataTableProps) {
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState(filters?.search || '')

  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? data : []
    setSelectedRows(newSelection)
    onSelectionChange?.(newSelection)
  }

  const handleSelectRow = (row: any, checked: boolean) => {
    const newSelection = checked 
      ? [...selectedRows, row]
      : selectedRows.filter(r => r.id !== row.id)
    setSelectedRows(newSelection)
    onSelectionChange?.(newSelection)
  }

  const handleSort = (field: string) => {
    if (!sorting) return
    
    const newDirection = sorting.field === field && sorting.direction === 'asc' ? 'desc' : 'asc'
    sorting.onSort(field, newDirection)
  }

  const getSortIcon = (field: string) => {
    if (!sorting || sorting.field !== field) {
      return <ArrowUpDown className="h-4 w-4" />
    }
    return sorting.direction === 'asc' 
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />
  }

  const renderCell = (column: Column, row: any) => {
    const value = row[column.key]
    
    if (column.render) {
      return column.render(value, row)
    }
    
    return value
  }

  const renderActions = (row: any) => {
    if (!actions) return null

    return (
      <div className="flex items-center space-x-2">
        {actions.onView && (
          <Button variant="ghost" size="sm" onClick={() => actions.onView!(row)}>
            <Eye className="h-4 w-4" />
          </Button>
        )}
        {actions.onEdit && (
          <Button variant="ghost" size="sm" onClick={() => actions.onEdit!(row)}>
            <Edit className="h-4 w-4" />
          </Button>
        )}
        {actions.onDelete && (
          <Button variant="ghost" size="sm" onClick={() => actions.onDelete!(row)}>
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        )}
        {actions.custom?.map((action, index) => (
          <Button
            key={index}
            variant={action.variant || 'ghost'}
            size="sm"
            onClick={() => action.onClick(row)}
          >
            {action.icon && <action.icon className="h-4 w-4" />}
            {action.label}
          </Button>
        ))}
      </div>
    )
  }

  return (
    <Card>
      {(title || filters || selectedRows.length > 0) && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              {title && <CardTitle>{title}</CardTitle>}
              {selectedRows.length > 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  已选择 {selectedRows.length} 项
                </p>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              {filters && (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value)
                      filters.onSearchChange(e.target.value)
                    }}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}
              
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                筛选
              </Button>
              
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {/* 表格 */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    {selectable && (
                      <th className="px-6 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedRows.length === data.length && data.length > 0}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                    )}
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className={cn(
                          'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                          column.sortable && 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
                        )}
                        style={{ width: column.width }}
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center space-x-1">
                          <span>{column.label}</span>
                          {column.sortable && getSortIcon(column.key)}
                        </div>
                      </th>
                    ))}
                    {actions && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {data.map((row, index) => (
                    <tr key={row.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      {selectable && (
                        <td className="px-6 py-4">
                          <input
                            type="checkbox"
                            checked={selectedRows.some(r => r.id === row.id)}
                            onChange={(e) => handleSelectRow(row, e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                      )}
                      {columns.map((column) => (
                        <td key={column.key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {renderCell(column, row)}
                        </td>
                      ))}
                      {actions && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {renderActions(row)}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {pagination && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span>每页显示</span>
                    <select
                      value={pagination.pageSize}
                      onChange={(e) => pagination.onPageSizeChange(Number(e.target.value))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      <option value={10}>10</option>
                      <option value={20}>20</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                    <span>条，共 {pagination.total} 条</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => pagination.onPageChange(1)}
                      disabled={pagination.page === 1}
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => pagination.onPageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    
                    <span className="text-sm text-gray-500">
                      第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页
                    </span>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => pagination.onPageChange(pagination.page + 1)}
                      disabled={pagination.page >= Math.ceil(pagination.total / pagination.pageSize)}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => pagination.onPageChange(Math.ceil(pagination.total / pagination.pageSize))}
                      disabled={pagination.page >= Math.ceil(pagination.total / pagination.pageSize)}
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
