'use client'

import React from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import DataTable from '@/components/admin/DataTable'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Package, 
  ShoppingCart, 
  CreditCard,
  TrendingUp,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

// 模拟数据
const mockUsers = [
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: 'user',
    vipLevel: 'Pro',
    status: 'active',
    createdAt: '2024-01-15',
    lastLogin: '2024-07-28'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    role: 'supplier',
    vipLevel: 'Enterprise',
    status: 'active',
    createdAt: '2024-02-20',
    lastLogin: '2024-07-27'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    role: 'buyer',
    vipLevel: 'Basic',
    status: 'inactive',
    createdAt: '2024-03-10',
    lastLogin: '2024-07-25'
  },
  {
    id: 4,
    name: '赵六',
    email: '<EMAIL>',
    role: 'admin',
    vipLevel: 'Enterprise',
    status: 'active',
    createdAt: '2024-01-01',
    lastLogin: '2024-07-29'
  },
]

const userColumns = [
  {
    key: 'name',
    label: '用户名',
    sortable: true,
  },
  {
    key: 'email',
    label: '邮箱',
    sortable: true,
  },
  {
    key: 'role',
    label: '角色',
    render: (value: string) => {
      const roleMap = {
        admin: { label: '管理员', color: 'bg-red-100 text-red-800' },
        user: { label: '用户', color: 'bg-blue-100 text-blue-800' },
        supplier: { label: '供应商', color: 'bg-green-100 text-green-800' },
        buyer: { label: '采购商', color: 'bg-purple-100 text-purple-800' },
      }
      const role = roleMap[value as keyof typeof roleMap] || { label: value, color: 'bg-gray-100 text-gray-800' }
      return (
        <Badge className={role.color}>
          {role.label}
        </Badge>
      )
    }
  },
  {
    key: 'vipLevel',
    label: 'VIP等级',
    render: (value: string) => {
      const vipMap = {
        Basic: { label: '基础版', color: 'bg-gray-100 text-gray-800' },
        Pro: { label: '专业版', color: 'bg-blue-100 text-blue-800' },
        Enterprise: { label: '企业版', color: 'bg-purple-100 text-purple-800' },
      }
      const vip = vipMap[value as keyof typeof vipMap] || { label: value, color: 'bg-gray-100 text-gray-800' }
      return (
        <Badge className={vip.color}>
          {vip.label}
        </Badge>
      )
    }
  },
  {
    key: 'status',
    label: '状态',
    render: (value: string) => (
      <Badge className={value === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
        {value === 'active' ? '活跃' : '非活跃'}
      </Badge>
    )
  },
  {
    key: 'lastLogin',
    label: '最后登录',
    sortable: true,
  },
]

export default function AdminDemoPage() {
  const [currentPage, setCurrentPage] = React.useState(1)
  const [pageSize, setPageSize] = React.useState(10)
  const [sortField, setSortField] = React.useState('name')
  const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('asc')
  const [searchTerm, setSearchTerm] = React.useState('')

  const handleSort = (field: string, direction: 'asc' | 'desc') => {
    setSortField(field)
    setSortDirection(direction)
  }

  const handleSearch = (search: string) => {
    setSearchTerm(search)
    setCurrentPage(1)
  }

  const handleView = (user: any) => {
    console.log('查看用户:', user)
  }

  const handleEdit = (user: any) => {
    console.log('编辑用户:', user)
  }

  const handleDelete = (user: any) => {
    console.log('删除用户:', user)
  }

  return (
    <AdminLayout 
      title="管理后台演示" 
      subtitle="展示完整的管理员界面功能"
    >
      <div className="space-y-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总用户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,248</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +12.5% 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总产品数</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3,456</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +8.2% 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订单数</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">789</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +15.3% 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总收入</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥234,567</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +23.1% 较上月
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 用户管理表格 */}
        <DataTable
          title="用户管理"
          columns={userColumns}
          data={mockUsers}
          pagination={{
            page: currentPage,
            pageSize: pageSize,
            total: mockUsers.length,
            onPageChange: setCurrentPage,
            onPageSizeChange: setPageSize,
          }}
          sorting={{
            field: sortField,
            direction: sortDirection,
            onSort: handleSort,
          }}
          filters={{
            search: searchTerm,
            onSearchChange: handleSearch,
          }}
          actions={{
            onView: handleView,
            onEdit: handleEdit,
            onDelete: handleDelete,
            custom: [
              {
                label: '重置密码',
                onClick: (user) => console.log('重置密码:', user),
                variant: 'outline',
              },
              {
                label: '发送邮件',
                onClick: (user) => console.log('发送邮件:', user),
                variant: 'outline',
              },
            ],
          }}
          selectable={true}
          onSelectionChange={(selectedRows) => {
            console.log('选中的用户:', selectedRows)
          }}
        />

        {/* 快捷操作 */}
        <Card>
          <CardHeader>
            <CardTitle>快捷操作</CardTitle>
            <CardDescription>
              常用的管理操作和功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex flex-col space-y-2">
                <Users className="h-6 w-6" />
                <span>添加用户</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col space-y-2">
                <Package className="h-6 w-6" />
                <span>添加产品</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col space-y-2">
                <ShoppingCart className="h-6 w-6" />
                <span>处理订单</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col space-y-2">
                <CreditCard className="h-6 w-6" />
                <span>财务报表</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 系统信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>数据库连接</span>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>文件存储</span>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>支付服务</span>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>邮件服务</span>
                <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span>新用户注册</span>
                  <span className="text-gray-500">2分钟前</span>
                </div>
                <div className="flex justify-between">
                  <span>新订单创建</span>
                  <span className="text-gray-500">5分钟前</span>
                </div>
                <div className="flex justify-between">
                  <span>产品审核通过</span>
                  <span className="text-gray-500">10分钟前</span>
                </div>
                <div className="flex justify-between">
                  <span>企业认证申请</span>
                  <span className="text-gray-500">15分钟前</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
