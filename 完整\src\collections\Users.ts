import type { CollectionConfig } from 'payload'
import { admins, adminsAndUser, anyone } from '../access/index'
import { checkRole } from '../access/checkRole'
import { loginAfterCreate } from '../hooks/loginAfterCreate'

export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 28800, // 8小时
    verify: {
      generateEmailHTML: ({ token, user }) => {
        return `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">欢迎加入B2B贸易平台</h2>
            <p>您好 ${user.name || user.email}，</p>
            <p>请点击下面的链接验证您的邮箱地址：</p>
            <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/verify-email?token=${token}" 
               style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              验证邮箱
            </a>
            <p>如果您没有注册账户，请忽略此邮件。</p>
            <p>此链接将在24小时后失效。</p>
          </div>
        `
      },
    },
    forgotPassword: {
      generateEmailHTML: ({ token, user }) => {
        return `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">重置密码</h2>
            <p>您好 ${user.name || user.email}，</p>
            <p>您请求重置密码。请点击下面的链接设置新密码：</p>
            <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/reset-password?token=${token}" 
               style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              重置密码
            </a>
            <p>如果您没有请求重置密码，请忽略此邮件。</p>
            <p>此链接将在1小时后失效。</p>
          </div>
        `
      },
    },
    cookies: {
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
      domain: process.env.COOKIE_DOMAIN,
    },
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'role', 'vipLevel', 'verified', 'createdAt'],
    listSearchableFields: ['name', 'email', 'company'],
    group: '用户管理',
  },
  access: {
    read: adminsAndUser,
    create: anyone,
    update: adminsAndUser,
    delete: admins,
    unlock: admins,
    admin: ({ req: { user } }) => checkRole(['admin'], user),
  },
  hooks: {
    afterChange: [loginAfterCreate],
    beforeChange: [
      ({ data, req, operation }) => {
        // 新用户默认设置
        if (operation === 'create') {
          data.points = data.points || 100 // 新用户赠送100积分
          data.vipLevel = data.vipLevel || 'Basic'
          data.verified = false
          data.status = 'active'
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'name',
      label: '姓名',
      type: 'text',
      required: true,
      admin: {
        placeholder: '请输入真实姓名',
      },
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      required: true,
      unique: true,
      admin: {
        placeholder: '请输入邮箱地址',
      },
    },
    {
      name: 'password',
      label: '密码',
      type: 'password',
      required: true,
      minLength: 6,
      admin: {
        description: '密码至少6位字符',
      },
    },
    {
      name: 'role',
      label: '角色',
      type: 'select',
      hasMany: true,
      saveToJWT: true,
      defaultValue: ['user'],
      access: {
        read: admins,
        update: admins,
        create: admins,
      },
      options: [
        {
          label: '管理员',
          value: 'admin',
        },
        {
          label: '普通用户',
          value: 'user',
        },
        {
          label: '供应商',
          value: 'supplier',
        },
        {
          label: '采购商',
          value: 'buyer',
        },
      ],
    },
    {
      name: 'avatar',
      label: '头像',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: '上传用户头像',
      },
    },
    {
      name: 'phone',
      label: '手机号',
      type: 'text',
      admin: {
        placeholder: '请输入手机号码',
      },
    },
    {
      name: 'company',
      label: '公司名称',
      type: 'text',
      admin: {
        placeholder: '请输入公司名称',
      },
    },
    {
      name: 'position',
      label: '职位',
      type: 'text',
      admin: {
        placeholder: '请输入职位',
      },
    },
    {
      name: 'industry',
      label: '行业',
      type: 'select',
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '机械设备', value: 'machinery' },
        { label: '化工原料', value: 'chemicals' },
        { label: '纺织服装', value: 'textiles' },
        { label: '建筑材料', value: 'construction' },
        { label: '食品饮料', value: 'food' },
        { label: '汽车配件', value: 'automotive' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'vipLevel',
      label: 'VIP等级',
      type: 'select',
      defaultValue: 'Basic',
      saveToJWT: true,
      options: [
        { label: '基础版', value: 'Basic' },
        { label: '专业版', value: 'Pro' },
        { label: '企业版', value: 'Enterprise' },
      ],
      admin: {
        description: 'VIP等级决定用户可访问的功能',
      },
    },
    {
      name: 'points',
      label: '积分',
      type: 'number',
      defaultValue: 0,
      min: 0,
      admin: {
        description: '用户当前积分余额',
      },
    },
    {
      name: 'verified',
      label: '已验证',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: '邮箱是否已验证',
      },
    },
    {
      name: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'active',
      options: [
        { label: '正常', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '待审核', value: 'pending' },
      ],
    },
    {
      name: 'lastLoginAt',
      label: '最后登录时间',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'preferences',
      label: '用户偏好',
      type: 'group',
      fields: [
        {
          name: 'language',
          label: '语言',
          type: 'select',
          defaultValue: 'zh',
          options: [
            { label: '中文', value: 'zh' },
            { label: 'English', value: 'en' },
          ],
        },
        {
          name: 'timezone',
          label: '时区',
          type: 'text',
          defaultValue: 'Asia/Shanghai',
        },
        {
          name: 'emailNotifications',
          label: '邮件通知',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'smsNotifications',
          label: '短信通知',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'companyProfile',
      label: '企业资料',
      type: 'group',
      admin: {
        condition: (data) => data.role?.includes('supplier') || data.role?.includes('buyer'),
      },
      fields: [
        {
          name: 'businessLicense',
          label: '营业执照',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'taxId',
          label: '税务登记号',
          type: 'text',
        },
        {
          name: 'address',
          label: '公司地址',
          type: 'textarea',
        },
        {
          name: 'website',
          label: '公司网站',
          type: 'text',
        },
        {
          name: 'description',
          label: '公司简介',
          type: 'textarea',
        },
        {
          name: 'verificationStatus',
          label: '认证状态',
          type: 'select',
          defaultValue: 'pending',
          options: [
            { label: '待审核', value: 'pending' },
            { label: '已认证', value: 'verified' },
            { label: '被拒绝', value: 'rejected' },
          ],
        },
      ],
    },
  ],
  timestamps: true,
}
