{"name": "b2b-trade-platform", "version": "1.0.0", "private": true, "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev -p 3001", "lint": "next lint", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "generate:schema": "cross-env NODE_OPTIONS=--no-deprecation payload generate:graphql-schema", "standalone-script": "cross-env NODE_OPTIONS=--no-deprecation payload run ./src/scripts/standalone-script.ts", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@payloadcms/db-mongodb": "^3.49.0", "@payloadcms/next": "^3.49.0", "@payloadcms/richtext-lexical": "^3.49.0", "@payloadcms/storage-uploadthing": "^3.49.0", "@payloadcms/ui": "^3.49.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cross-env": "^7.0.3", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.6", "graphql": "^16.8.1", "input-otp": "1.4.1", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "mongodb": "^6.3.0", "next": "15.2.4", "next-themes": "^0.4.4", "payload": "^3.49.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sharp": "^0.32.6", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.0", "uuid": "^9.0.1", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "eslint": "^8", "eslint-config-next": "15.2.4", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}, "engines": {"node": "^18.20.2 || >=20.9.0"}}