import type { GlobalConfig } from 'payload'
import { admins } from '../access/index'

export const SiteConfig: GlobalConfig = {
  slug: 'site-config',
  label: '站点配置',
  access: {
    read: () => true,
    update: admins,
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: '首页配置',
          fields: [
            {
              name: 'hero',
              label: '首页横幅',
              type: 'group',
              fields: [
                {
                  name: 'title',
                  label: '主标题',
                  type: 'text',
                  required: true,
                  defaultValue: '全球领先的B2B贸易平台',
                },
                {
                  name: 'subtitle',
                  label: '副标题',
                  type: 'text',
                  defaultValue: '连接全球供应商与采购商，打造高效贸易生态',
                },
                {
                  name: 'description',
                  label: '描述',
                  type: 'textarea',
                  defaultValue: '我们致力于为全球企业提供专业的B2B贸易服务，通过先进的技术和优质的服务，帮助企业拓展国际市场，实现商业成功。',
                },
                {
                  name: 'backgroundImage',
                  label: '背景图片',
                  type: 'upload',
                  relationTo: 'media',
                },
                {
                  name: 'ctaButtons',
                  label: '行动按钮',
                  type: 'array',
                  maxRows: 3,
                  fields: [
                    {
                      name: 'text',
                      label: '按钮文字',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'url',
                      label: '链接地址',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'style',
                      label: '按钮样式',
                      type: 'select',
                      defaultValue: 'primary',
                      options: [
                        { label: '主要', value: 'primary' },
                        { label: '次要', value: 'secondary' },
                        { label: '轮廓', value: 'outline' },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              name: 'features',
              label: '特色功能',
              type: 'array',
              fields: [
                {
                  name: 'title',
                  label: '功能标题',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  label: '功能描述',
                  type: 'textarea',
                  required: true,
                },
                {
                  name: 'icon',
                  label: '图标',
                  type: 'text',
                  admin: {
                    description: '图标类名或SVG代码',
                  },
                },
                {
                  name: 'image',
                  label: '功能图片',
                  type: 'upload',
                  relationTo: 'media',
                },
                {
                  name: 'link',
                  label: '链接地址',
                  type: 'text',
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
              ],
            },
            {
              name: 'statistics',
              label: '统计数据',
              type: 'array',
              fields: [
                {
                  name: 'label',
                  label: '标签',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'value',
                  label: '数值',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'suffix',
                  label: '后缀',
                  type: 'text',
                  admin: {
                    description: '如：万、+、%等',
                  },
                },
                {
                  name: 'icon',
                  label: '图标',
                  type: 'text',
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
              ],
            },
          ],
        },
        {
          label: '产品展示',
          fields: [
            {
              name: 'featuredProducts',
              label: '推荐产品设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用推荐产品',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'title',
                  label: '区块标题',
                  type: 'text',
                  defaultValue: '推荐产品',
                },
                {
                  name: 'subtitle',
                  label: '区块副标题',
                  type: 'text',
                  defaultValue: '精选优质产品，满足您的采购需求',
                },
                {
                  name: 'limit',
                  label: '显示数量',
                  type: 'number',
                  defaultValue: 8,
                  min: 1,
                  max: 20,
                },
                {
                  name: 'layout',
                  label: '布局样式',
                  type: 'select',
                  defaultValue: 'grid',
                  options: [
                    { label: '网格布局', value: 'grid' },
                    { label: '轮播布局', value: 'carousel' },
                    { label: '列表布局', value: 'list' },
                  ],
                },
              ],
            },
            {
              name: 'categories',
              label: '分类展示设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用分类展示',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'title',
                  label: '区块标题',
                  type: 'text',
                  defaultValue: '产品分类',
                },
                {
                  name: 'subtitle',
                  label: '区块副标题',
                  type: 'text',
                  defaultValue: '浏览各类产品，找到您需要的商品',
                },
                {
                  name: 'limit',
                  label: '显示数量',
                  type: 'number',
                  defaultValue: 12,
                  min: 1,
                  max: 24,
                },
                {
                  name: 'showProductCount',
                  label: '显示产品数量',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
        {
          label: '公司展示',
          fields: [
            {
              name: 'featuredCompanies',
              label: '推荐企业设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用推荐企业',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'title',
                  label: '区块标题',
                  type: 'text',
                  defaultValue: '优质企业',
                },
                {
                  name: 'subtitle',
                  label: '区块副标题',
                  type: 'text',
                  defaultValue: '与认证企业合作，保障交易安全',
                },
                {
                  name: 'limit',
                  label: '显示数量',
                  type: 'number',
                  defaultValue: 6,
                  min: 1,
                  max: 12,
                },
                {
                  name: 'criteria',
                  label: '筛选条件',
                  type: 'select',
                  defaultValue: 'verified',
                  options: [
                    { label: '已认证企业', value: 'verified' },
                    { label: '高评级企业', value: 'high_rating' },
                    { label: '活跃企业', value: 'active' },
                    { label: '手动选择', value: 'manual' },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: '新闻资讯',
          fields: [
            {
              name: 'news',
              label: '新闻资讯设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用新闻资讯',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'title',
                  label: '区块标题',
                  type: 'text',
                  defaultValue: '行业资讯',
                },
                {
                  name: 'subtitle',
                  label: '区块副标题',
                  type: 'text',
                  defaultValue: '了解最新行业动态和市场趋势',
                },
                {
                  name: 'limit',
                  label: '显示数量',
                  type: 'number',
                  defaultValue: 6,
                  min: 1,
                  max: 12,
                },
                {
                  name: 'showDate',
                  label: '显示发布日期',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'showAuthor',
                  label: '显示作者',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
          ],
        },
        {
          label: '合作伙伴',
          fields: [
            {
              name: 'partners',
              label: '合作伙伴',
              type: 'array',
              fields: [
                {
                  name: 'name',
                  label: '合作伙伴名称',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'logo',
                  label: 'Logo',
                  type: 'upload',
                  relationTo: 'media',
                  required: true,
                },
                {
                  name: 'website',
                  label: '官网链接',
                  type: 'text',
                },
                {
                  name: 'description',
                  label: '描述',
                  type: 'textarea',
                },
                {
                  name: 'order',
                  label: '排序',
                  type: 'number',
                  defaultValue: 0,
                },
                {
                  name: 'featured',
                  label: '重点展示',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
          ],
        },
        {
          label: '页脚配置',
          fields: [
            {
              name: 'footer',
              label: '页脚设置',
              type: 'group',
              fields: [
                {
                  name: 'companyInfo',
                  label: '公司信息',
                  type: 'group',
                  fields: [
                    {
                      name: 'name',
                      label: '公司名称',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'description',
                      label: '公司描述',
                      type: 'textarea',
                    },
                    {
                      name: 'logo',
                      label: '页脚Logo',
                      type: 'upload',
                      relationTo: 'media',
                    },
                  ],
                },
                {
                  name: 'copyright',
                  label: '版权信息',
                  type: 'text',
                  defaultValue: '© 2024 B2B贸易平台. 保留所有权利.',
                },
                {
                  name: 'icp',
                  label: 'ICP备案号',
                  type: 'text',
                },
                {
                  name: 'showSocialMedia',
                  label: '显示社交媒体',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'showPartners',
                  label: '显示合作伙伴',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
