// PM2 配置文件 - 用于宝塔面板部署
module.exports = {
  apps: [
    {
      name: 'b2b-trade-platform',
      script: 'npm',
      args: 'start',
      cwd: '/www/wwwroot/yourdomain.com', // 修改为实际路径
      instances: 'max', // 使用所有CPU核心
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
      },
      // 日志配置
      log_file: '/www/wwwroot/yourdomain.com/logs/combined.log',
      out_file: '/www/wwwroot/yourdomain.com/logs/out.log',
      error_file: '/www/wwwroot/yourdomain.com/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 自动重启配置
      watch: false, // 生产环境不建议开启
      ignore_watch: ['node_modules', 'logs', '.git'],
      max_memory_restart: '1G',
      
      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,
      autorestart: true,
      
      // 其他配置
      merge_logs: true,
      time: true,
      
      // 环境变量
      env_file: '.env.local',
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'root',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: 'https://github.com/yourusername/b2b-trade-platform.git',
      path: '/www/wwwroot/yourdomain.com',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
}
