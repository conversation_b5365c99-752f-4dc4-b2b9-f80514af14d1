import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../../../payload.config'

// 获取单个用户详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayload({ config })
    const { id } = params

    const user = await payload.findByID({
      collection: 'users',
      id,
      depth: 2,
    })

    // 获取用户相关统计
    const userOrders = await payload.count({
      collection: 'orders',
      where: { user: { equals: id } },
    })

    const userPayments = await payload.find({
      collection: 'payments',
      where: { user: { equals: id } },
      limit: 10,
      sort: '-createdAt',
    })

    const userSubscriptions = await payload.find({
      collection: 'subscriptions',
      where: { user: { equals: id } },
      limit: 5,
      sort: '-createdAt',
    })

    const userPointsTransactions = await payload.find({
      collection: 'pointsTransactions',
      where: { user: { equals: id } },
      limit: 10,
      sort: '-createdAt',
    })

    const userAccessLogs = await payload.find({
      collection: 'accessLogs',
      where: { user: { equals: id } },
      limit: 20,
      sort: '-createdAt',
    })

    return NextResponse.json({
      success: true,
      data: {
        user,
        stats: {
          totalOrders: userOrders.totalDocs,
          recentPayments: userPayments.docs,
          activeSubscriptions: userSubscriptions.docs,
          pointsTransactions: userPointsTransactions.docs,
          recentAccess: userAccessLogs.docs,
        },
      },
    })
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取用户详情失败' },
      { status: 500 }
    )
  }
}

// 更新用户信息
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayload({ config })
    const { id } = params
    const body = await request.json()

    // 如果更新邮箱，检查是否已存在
    if (body.email) {
      const existingUser = await payload.find({
        collection: 'users',
        where: {
          and: [
            { email: { equals: body.email } },
            { id: { not_equals: id } },
          ],
        },
        limit: 1,
      })

      if (existingUser.docs.length > 0) {
        return NextResponse.json(
          { success: false, error: '该邮箱已被其他用户使用' },
          { status: 400 }
        )
      }
    }

    // 准备更新数据
    const updateData: any = {}
    
    // 基本信息
    if (body.name !== undefined) updateData.name = body.name
    if (body.email !== undefined) updateData.email = body.email
    if (body.phone !== undefined) updateData.phone = body.phone
    if (body.company !== undefined) updateData.company = body.company
    if (body.role !== undefined) updateData.role = body.role
    if (body.vipLevel !== undefined) updateData.vipLevel = body.vipLevel
    if (body.status !== undefined) updateData.status = body.status
    if (body.points !== undefined) updateData.points = body.points
    if (body.avatar !== undefined) updateData.avatar = body.avatar
    if (body.address !== undefined) updateData.address = body.address
    if (body.website !== undefined) updateData.website = body.website
    if (body.description !== undefined) updateData.description = body.description

    // 偏好设置
    if (body.preferences) {
      updateData.preferences = body.preferences
    }

    // 如果有新密码，更新密码
    if (body.newPassword) {
      updateData.password = body.newPassword
    }

    const updatedUser = await payload.update({
      collection: 'users',
      id,
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: '用户信息更新成功',
    })
  } catch (error) {
    console.error('更新用户失败:', error)
    return NextResponse.json(
      { success: false, error: '更新用户失败' },
      { status: 500 }
    )
  }
}

// 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayload({ config })
    const { id } = params

    // 检查用户是否存在
    const user = await payload.findByID({
      collection: 'users',
      id,
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 检查是否为超级管理员
    if (user.role === 'super-admin') {
      return NextResponse.json(
        { success: false, error: '不能删除超级管理员账户' },
        { status: 403 }
      )
    }

    // 软删除：将状态设为已删除，而不是真正删除
    const deletedUser = await payload.update({
      collection: 'users',
      id,
      data: {
        status: 'deleted',
        email: `deleted_${Date.now()}_${user.email}`, // 避免邮箱冲突
      },
    })

    return NextResponse.json({
      success: true,
      data: deletedUser,
      message: '用户删除成功',
    })
  } catch (error) {
    console.error('删除用户失败:', error)
    return NextResponse.json(
      { success: false, error: '删除用户失败' },
      { status: 500 }
    )
  }
}

// 重置用户密码
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayload({ config })
    const { id } = params
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'resetPassword':
        // 生成临时密码
        const tempPassword = Math.random().toString(36).slice(-8)
        
        const user = await payload.update({
          collection: 'users',
          id,
          data: {
            password: tempPassword,
            // 标记需要在下次登录时更改密码
            mustChangePassword: true,
          },
        })

        // 这里可以发送邮件通知用户新密码
        // await sendPasswordResetEmail(user.email, tempPassword)

        return NextResponse.json({
          success: true,
          data: { tempPassword },
          message: '密码重置成功，临时密码已生成',
        })

      case 'sendVerificationEmail':
        // 重新发送验证邮件
        await payload.sendEmail({
          to: body.email,
          subject: '邮箱验证',
          html: `请点击链接验证邮箱: ${process.env.NEXT_PUBLIC_SERVER_URL}/verify-email?token=${body.token}`,
        })

        return NextResponse.json({
          success: true,
          message: '验证邮件已发送',
        })

      case 'toggleStatus':
        const currentUser = await payload.findByID({
          collection: 'users',
          id,
        })

        const newStatus = currentUser.status === 'active' ? 'inactive' : 'active'
        
        const updatedUser = await payload.update({
          collection: 'users',
          id,
          data: { status: newStatus },
        })

        return NextResponse.json({
          success: true,
          data: updatedUser,
          message: `用户状态已${newStatus === 'active' ? '激活' : '禁用'}`,
        })

      case 'upgradeVip':
        if (!body.vipLevel) {
          return NextResponse.json(
            { success: false, error: 'VIP等级不能为空' },
            { status: 400 }
          )
        }

        const vipUser = await payload.update({
          collection: 'users',
          id,
          data: { 
            vipLevel: body.vipLevel,
            vipExpiredAt: body.vipExpiredAt || null,
          },
        })

        return NextResponse.json({
          success: true,
          data: vipUser,
          message: 'VIP等级更新成功',
        })

      case 'addPoints':
        if (!body.points || isNaN(body.points)) {
          return NextResponse.json(
            { success: false, error: '积分数量无效' },
            { status: 400 }
          )
        }

        const pointsUser = await payload.findByID({
          collection: 'users',
          id,
        })

        const updatedPointsUser = await payload.update({
          collection: 'users',
          id,
          data: { 
            points: (pointsUser.points || 0) + parseInt(body.points),
          },
        })

        // 记录积分交易
        await payload.create({
          collection: 'pointsTransactions',
          data: {
            user: id,
            type: body.points > 0 ? 'earn' : 'spend',
            amount: Math.abs(body.points),
            description: body.description || '管理员操作',
            status: 'completed',
          },
        })

        return NextResponse.json({
          success: true,
          data: updatedPointsUser,
          message: '积分更新成功',
        })

      default:
        return NextResponse.json(
          { success: false, error: '不支持的操作' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('用户操作失败:', error)
    return NextResponse.json(
      { success: false, error: '操作失败' },
      { status: 500 }
    )
  }
}
