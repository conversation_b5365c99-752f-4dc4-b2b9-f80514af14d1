import type { CollectionConfig } from 'payload'
import { admins, anyone } from '../access/index'

export const Categories: CollectionConfig = {
  slug: 'categories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'parent', 'productCount', 'status'],
    listSearchableFields: ['name', 'description'],
    group: '产品管理',
  },
  access: {
    read: anyone,
    create: admins,
    update: admins,
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        if (operation === 'create') {
          // 生成slug
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-|-$/g, '')
          }
          
          // 设置默认状态
          data.status = data.status || 'active'
          data.productCount = 0
        }
        return data
      },
    ],
  },
  fields: [
    {
      name: 'name',
      label: '分类名称',
      type: 'text',
      required: true,
      admin: {
        placeholder: '请输入分类名称',
      },
    },
    {
      name: 'slug',
      label: 'URL别名',
      type: 'text',
      unique: true,
      admin: {
        description: '用于URL的唯一标识符',
      },
    },
    {
      name: 'description',
      label: '分类描述',
      type: 'textarea',
      admin: {
        placeholder: '请输入分类描述',
      },
    },
    {
      name: 'parent',
      label: '父分类',
      type: 'relationship',
      relationTo: 'categories',
      admin: {
        description: '选择父分类，留空表示顶级分类',
      },
    },
    {
      name: 'image',
      label: '分类图片',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: '分类展示图片',
      },
    },
    {
      name: 'icon',
      label: '分类图标',
      type: 'text',
      admin: {
        description: '图标类名或SVG代码',
        placeholder: '如：fas fa-laptop 或 SVG代码',
      },
    },
    {
      name: 'color',
      label: '主题色',
      type: 'text',
      admin: {
        description: '分类主题颜色（十六进制）',
        placeholder: '#007bff',
      },
    },
    {
      name: 'status',
      label: '状态',
      type: 'select',
      defaultValue: 'active',
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '隐藏', value: 'hidden' },
      ],
    },
    {
      name: 'featured',
      label: '推荐分类',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: '是否在首页推荐显示',
      },
    },
    {
      name: 'sortOrder',
      label: '排序',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: '数字越小排序越靠前',
      },
    },
    {
      name: 'productCount',
      label: '产品数量',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: '该分类下的产品数量',
      },
    },
    {
      name: 'attributes',
      label: '分类属性',
      type: 'array',
      admin: {
        description: '该分类产品的通用属性',
      },
      fields: [
        {
          name: 'name',
          label: '属性名称',
          type: 'text',
          required: true,
        },
        {
          name: 'type',
          label: '属性类型',
          type: 'select',
          required: true,
          options: [
            { label: '文本', value: 'text' },
            { label: '数字', value: 'number' },
            { label: '选择', value: 'select' },
            { label: '多选', value: 'multiselect' },
            { label: '布尔', value: 'boolean' },
          ],
        },
        {
          name: 'options',
          label: '选项值',
          type: 'array',
          admin: {
            condition: (data, siblingData) => 
              siblingData?.type === 'select' || siblingData?.type === 'multiselect',
          },
          fields: [
            {
              name: 'label',
              label: '显示名称',
              type: 'text',
              required: true,
            },
            {
              name: 'value',
              label: '值',
              type: 'text',
              required: true,
            },
          ],
        },
        {
          name: 'required',
          label: '必填',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'searchable',
          label: '可搜索',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'filterable',
          label: '可筛选',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'seo',
      label: 'SEO设置',
      type: 'group',
      fields: [
        {
          name: 'title',
          label: 'SEO标题',
          type: 'text',
          maxLength: 60,
        },
        {
          name: 'description',
          label: 'SEO描述',
          type: 'textarea',
          maxLength: 160,
        },
        {
          name: 'keywords',
          label: '关键词',
          type: 'text',
          hasMany: true,
        },
      ],
    },
    {
      name: 'customFields',
      label: '自定义字段',
      type: 'json',
      admin: {
        description: '存储额外的自定义数据',
      },
    },
  ],
  timestamps: true,
}
