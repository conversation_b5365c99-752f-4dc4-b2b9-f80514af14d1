import type { CollectionConfig } from 'payload'
import { admins, adminsAndUser } from '../access/index'

export const PointsTransactions: CollectionConfig = {
  slug: 'points-transactions',
  admin: {
    useAsTitle: 'transactionId',
    defaultColumns: ['transactionId', 'user', 'type', 'amount', 'status', 'createdAt'],
    listSearchableFields: ['transactionId', 'description'],
    group: '积分管理',
  },
  access: {
    read: adminsAndUser,
    create: adminsAndUser,
    update: admins,
    delete: admins,
  },
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        if (operation === 'create') {
          // 生成交易ID
          if (!data.transactionId) {
            const timestamp = Date.now()
            const random = Math.random().toString(36).substr(2, 6).toUpperCase()
            data.transactionId = `PTS-${timestamp}-${random}`
          }
          
          // 设置默认状态
          data.status = data.status || 'completed'
          
          // 设置用户
          if (!data.user && req.user) {
            data.user = req.user.id
          }
        }
        return data
      },
    ],
    afterChange: [
      async ({ doc, req, operation }) => {
        // 更新用户积分余额
        if ((operation === 'create' || operation === 'update') && doc.status === 'completed') {
          const user = await req.payload.findByID({
            collection: 'users',
            id: doc.user,
          })
          
          if (user) {
            let newPoints = user.points || 0
            
            if (doc.type === 'earn' || doc.type === 'reward' || doc.type === 'refund') {
              newPoints += doc.amount
            } else if (doc.type === 'spend' || doc.type === 'deduct') {
              newPoints -= doc.amount
            }
            
            // 确保积分不为负数
            newPoints = Math.max(0, newPoints)
            
            await req.payload.update({
              collection: 'users',
              id: doc.user,
              data: {
                points: newPoints,
              },
            })
          }
        }
      },
    ],
  },
  fields: [
    {
      name: 'transactionId',
      label: '交易ID',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'user',
      label: '用户',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'type',
      label: '交易类型',
      type: 'select',
      required: true,
      options: [
        { label: '获得积分', value: 'earn' },
        { label: '消费积分', value: 'spend' },
        { label: '奖励积分', value: 'reward' },
        { label: '扣除积分', value: 'deduct' },
        { label: '退还积分', value: 'refund' },
        { label: '转入积分', value: 'transfer_in' },
        { label: '转出积分', value: 'transfer_out' },
        { label: '过期扣除', value: 'expire' },
      ],
    },
    {
      name: 'amount',
      label: '积分数量',
      type: 'number',
      required: true,
      min: 0,
    },
    {
      name: 'balanceBefore',
      label: '交易前余额',
      type: 'number',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'balanceAfter',
      label: '交易后余额',
      type: 'number',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'status',
      label: '交易状态',
      type: 'select',
      defaultValue: 'completed',
      options: [
        { label: '待处理', value: 'pending' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已失败', value: 'failed' },
      ],
    },
    {
      name: 'source',
      label: '积分来源',
      type: 'select',
      options: [
        { label: '注册奖励', value: 'registration' },
        { label: '登录奖励', value: 'login' },
        { label: '完善资料', value: 'profile_complete' },
        { label: '邀请好友', value: 'referral' },
        { label: '发布产品', value: 'product_publish' },
        { label: '查看产品', value: 'product_view' },
        { label: '下载资料', value: 'download' },
        { label: '购买充值', value: 'purchase' },
        { label: '订单完成', value: 'order_complete' },
        { label: '评价奖励', value: 'review' },
        { label: '活动奖励', value: 'activity' },
        { label: '管理员调整', value: 'admin_adjust' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      name: 'description',
      label: '交易描述',
      type: 'text',
      required: true,
    },
    {
      name: 'relatedOrder',
      label: '关联订单',
      type: 'relationship',
      relationTo: 'orders',
      admin: {
        description: '如果与订单相关，选择对应订单',
      },
    },
    {
      name: 'relatedProduct',
      label: '关联产品',
      type: 'relationship',
      relationTo: 'products',
      admin: {
        description: '如果与产品相关，选择对应产品',
      },
    },
    {
      name: 'relatedUser',
      label: '关联用户',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: '如果是转账或邀请，选择相关用户',
      },
    },
    {
      name: 'expiryDate',
      label: '过期时间',
      type: 'date',
      admin: {
        description: '积分过期时间，留空表示永不过期',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'operator',
      label: '操作人',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: '执行此操作的用户（如管理员调整）',
      },
    },
    {
      name: 'ipAddress',
      label: 'IP地址',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'userAgent',
      label: '用户代理',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'notes',
      label: '备注',
      type: 'textarea',
    },
    {
      name: 'internalNotes',
      label: '内部备注',
      type: 'textarea',
      access: {
        read: admins,
        update: admins,
      },
    },
    {
      name: 'metadata',
      label: '元数据',
      type: 'json',
      admin: {
        description: '存储额外的交易相关数据',
      },
    },
  ],
  timestamps: true,
}
