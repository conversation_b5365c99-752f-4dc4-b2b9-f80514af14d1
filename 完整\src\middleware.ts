import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtVerify } from 'jose'

// 需要认证的路径
const protectedPaths = [
  '/admin',
  '/admin-demo',
  '/admin-backend',
  '/api/admin',
]

// 需要管理员权限的路径
const adminPaths = [
  '/admin',
  '/admin-demo',
  '/admin-backend',
  '/api/admin',
]

// 公开的API路径
const publicApiPaths = [
  '/api/auth',
  '/api/public',
  '/api/webhook',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查是否是受保护的路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path))
  const isAdminPath = adminPaths.some(path => pathname.startsWith(path))
  const isPublicApiPath = publicApiPaths.some(path => pathname.startsWith(path))

  // 如果是公开API路径，直接通过
  if (isPublicApiPath) {
    return NextResponse.next()
  }

  // 如果不是受保护的路径，直接通过
  if (!isProtectedPath) {
    return NextResponse.next()
  }

  // 获取认证token
  const token = request.cookies.get('payload-token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '')

  if (!token) {
    // 如果是API请求，返回401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }
    
    // 如果是页面请求，重定向到登录页
    const loginUrl = new URL('/admin/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }

  try {
    // 验证JWT token
    const secret = new TextEncoder().encode(process.env.PAYLOAD_SECRET || 'your-secret-key')
    const { payload } = await jwtVerify(token, secret)

    // 检查用户信息
    if (!payload.id || !payload.email) {
      throw new Error('Invalid token payload')
    }

    // 如果是管理员路径，检查管理员权限
    if (isAdminPath) {
      const userRole = payload.role as string
      if (!userRole || !['admin', 'super-admin'].includes(userRole)) {
        // 如果是API请求，返回403
        if (pathname.startsWith('/api/')) {
          return NextResponse.json(
            { success: false, error: '权限不足' },
            { status: 403 }
          )
        }
        
        // 如果是页面请求，重定向到无权限页面
        return NextResponse.redirect(new URL('/unauthorized', request.url))
      }
    }

    // 在请求头中添加用户信息，供API使用
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', payload.id as string)
    requestHeaders.set('x-user-email', payload.email as string)
    requestHeaders.set('x-user-role', payload.role as string || 'user')

    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })
  } catch (error) {
    console.error('Token verification failed:', error)
    
    // 清除无效的token
    const response = NextResponse.redirect(new URL('/admin/login', request.url))
    response.cookies.delete('payload-token')
    
    // 如果是API请求，返回401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: '认证失败' },
        { status: 401 }
      )
    }
    
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
