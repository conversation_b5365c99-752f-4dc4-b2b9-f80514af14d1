import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 需要认证的路径
const protectedPaths = [
  '/admin',
  '/admin-demo',
  '/admin-backend',
  '/api/admin',
]

// 需要管理员权限的路径
const adminPaths = [
  '/admin',
  '/admin-demo',
  '/admin-backend',
  '/api/admin',
]

// 公开的API路径
const publicApiPaths = [
  '/api/auth',
  '/api/public',
  '/api/webhook',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查是否是受保护的路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path))
  const isAdminPath = adminPaths.some(path => pathname.startsWith(path))
  const isPublicApiPath = publicApiPaths.some(path => pathname.startsWith(path))

  // 如果是公开API路径，直接通过
  if (isPublicApiPath) {
    return NextResponse.next()
  }

  // 如果不是受保护的路径，直接通过
  if (!isProtectedPath) {
    return NextResponse.next()
  }

  // 获取认证token
  const token = request.cookies.get('payload-token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '')

  if (!token) {
    // 如果是API请求，返回401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      )
    }

    // 如果是页面请求，重定向到登录页
    const loginUrl = new URL('/admin/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // 简化的token验证（实际项目中应该使用JWT验证）
  try {
    // 这里应该验证JWT token，暂时简化处理
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      role: 'admin'
    }

    // 如果是管理员路径，检查管理员权限
    if (isAdminPath) {
      const userRole = mockUser.role
      if (!userRole || !['admin', 'super-admin'].includes(userRole)) {
        // 如果是API请求，返回403
        if (pathname.startsWith('/api/')) {
          return NextResponse.json(
            { success: false, error: '权限不足' },
            { status: 403 }
          )
        }

        // 如果是页面请求，重定向到无权限页面
        return NextResponse.redirect(new URL('/unauthorized', request.url))
      }
    }

    // 在请求头中添加用户信息，供API使用
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', mockUser.id)
    requestHeaders.set('x-user-email', mockUser.email)
    requestHeaders.set('x-user-role', mockUser.role)

    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })
  } catch (error) {
    console.error('Token verification failed:', error)

    // 如果是API请求，返回401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: '认证失败' },
        { status: 401 }
      )
    }

    // 清除无效的token并重定向
    const response = NextResponse.redirect(new URL('/admin/login', request.url))
    response.cookies.delete('payload-token')
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
