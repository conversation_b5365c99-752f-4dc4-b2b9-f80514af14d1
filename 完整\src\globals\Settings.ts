import type { GlobalConfig } from 'payload'
import { admins } from '../access/index'

export const Settings: GlobalConfig = {
  slug: 'settings',
  label: '系统设置',
  access: {
    read: () => true,
    update: admins,
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: '基本设置',
          fields: [
            {
              name: 'siteName',
              label: '网站名称',
              type: 'text',
              required: true,
              defaultValue: 'B2B贸易信息平台',
            },
            {
              name: 'siteDescription',
              label: '网站描述',
              type: 'textarea',
              defaultValue: '专业的B2B贸易信息平台，连接全球供应商和采购商',
            },
            {
              name: 'siteKeywords',
              label: '网站关键词',
              type: 'text',
              hasMany: true,
            },
            {
              name: 'logo',
              label: '网站Logo',
              type: 'upload',
              relationTo: 'media',
            },
            {
              name: 'favicon',
              label: '网站图标',
              type: 'upload',
              relationTo: 'media',
            },
            {
              name: 'defaultLanguage',
              label: '默认语言',
              type: 'select',
              defaultValue: 'zh',
              options: [
                { label: '中文', value: 'zh' },
                { label: 'English', value: 'en' },
              ],
            },
            {
              name: 'timezone',
              label: '时区',
              type: 'text',
              defaultValue: 'Asia/Shanghai',
            },
            {
              name: 'currency',
              label: '默认货币',
              type: 'select',
              defaultValue: 'CNY',
              options: [
                { label: '人民币 (CNY)', value: 'CNY' },
                { label: '美元 (USD)', value: 'USD' },
                { label: '欧元 (EUR)', value: 'EUR' },
              ],
            },
          ],
        },
        {
          label: '联系信息',
          fields: [
            {
              name: 'contactEmail',
              label: '联系邮箱',
              type: 'email',
              required: true,
            },
            {
              name: 'supportEmail',
              label: '客服邮箱',
              type: 'email',
            },
            {
              name: 'contactPhone',
              label: '联系电话',
              type: 'text',
            },
            {
              name: 'supportPhone',
              label: '客服电话',
              type: 'text',
            },
            {
              name: 'address',
              label: '公司地址',
              type: 'textarea',
            },
            {
              name: 'workingHours',
              label: '工作时间',
              type: 'text',
              defaultValue: '周一至周五 9:00-18:00',
            },
          ],
        },
        {
          label: '社交媒体',
          fields: [
            {
              name: 'socialMedia',
              label: '社交媒体',
              type: 'group',
              fields: [
                {
                  name: 'wechat',
                  label: '微信',
                  type: 'text',
                },
                {
                  name: 'weibo',
                  label: '微博',
                  type: 'text',
                },
                {
                  name: 'linkedin',
                  label: 'LinkedIn',
                  type: 'text',
                },
                {
                  name: 'facebook',
                  label: 'Facebook',
                  type: 'text',
                },
                {
                  name: 'twitter',
                  label: 'Twitter',
                  type: 'text',
                },
                {
                  name: 'youtube',
                  label: 'YouTube',
                  type: 'text',
                },
              ],
            },
          ],
        },
        {
          label: 'VIP设置',
          fields: [
            {
              name: 'vipPlans',
              label: 'VIP计划',
              type: 'array',
              fields: [
                {
                  name: 'name',
                  label: '计划名称',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'level',
                  label: '等级',
                  type: 'select',
                  required: true,
                  options: [
                    { label: '基础版', value: 'Basic' },
                    { label: '专业版', value: 'Pro' },
                    { label: '企业版', value: 'Enterprise' },
                  ],
                },
                {
                  name: 'price',
                  label: '价格',
                  type: 'group',
                  fields: [
                    {
                      name: 'monthly',
                      label: '月付价格',
                      type: 'number',
                      min: 0,
                    },
                    {
                      name: 'quarterly',
                      label: '季付价格',
                      type: 'number',
                      min: 0,
                    },
                    {
                      name: 'yearly',
                      label: '年付价格',
                      type: 'number',
                      min: 0,
                    },
                  ],
                },
                {
                  name: 'features',
                  label: '功能特性',
                  type: 'array',
                  fields: [
                    {
                      name: 'name',
                      label: '功能名称',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'limit',
                      label: '限制数量',
                      type: 'number',
                      admin: {
                        description: '-1表示无限制',
                      },
                    },
                    {
                      name: 'description',
                      label: '功能描述',
                      type: 'text',
                    },
                  ],
                },
                {
                  name: 'description',
                  label: '计划描述',
                  type: 'richText',
                },
                {
                  name: 'popular',
                  label: '推荐计划',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
          ],
        },
        {
          label: '积分设置',
          fields: [
            {
              name: 'pointsSettings',
              label: '积分设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用积分系统',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'exchangeRate',
                  label: '积分兑换比例',
                  type: 'number',
                  defaultValue: 100,
                  admin: {
                    description: '1元 = 多少积分',
                  },
                },
                {
                  name: 'rewards',
                  label: '积分奖励规则',
                  type: 'array',
                  fields: [
                    {
                      name: 'action',
                      label: '操作类型',
                      type: 'select',
                      required: true,
                      options: [
                        { label: '注册奖励', value: 'registration' },
                        { label: '每日登录', value: 'daily_login' },
                        { label: '完善资料', value: 'profile_complete' },
                        { label: '邀请好友', value: 'referral' },
                        { label: '发布产品', value: 'product_publish' },
                        { label: '订单完成', value: 'order_complete' },
                        { label: '评价奖励', value: 'review' },
                      ],
                    },
                    {
                      name: 'points',
                      label: '奖励积分',
                      type: 'number',
                      required: true,
                      min: 0,
                    },
                    {
                      name: 'limit',
                      label: '每日限制',
                      type: 'number',
                      admin: {
                        description: '每日最多获得次数，0表示无限制',
                      },
                    },
                  ],
                },
                {
                  name: 'expiry',
                  label: '积分过期设置',
                  type: 'group',
                  fields: [
                    {
                      name: 'enabled',
                      label: '启用积分过期',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                    {
                      name: 'days',
                      label: '过期天数',
                      type: 'number',
                      defaultValue: 365,
                      admin: {
                        condition: (data, siblingData) => siblingData?.enabled,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: '邮件设置',
          fields: [
            {
              name: 'emailSettings',
              label: '邮件设置',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用邮件服务',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'fromName',
                  label: '发件人名称',
                  type: 'text',
                  defaultValue: 'B2B贸易平台',
                },
                {
                  name: 'fromEmail',
                  label: '发件人邮箱',
                  type: 'email',
                },
                {
                  name: 'replyTo',
                  label: '回复邮箱',
                  type: 'email',
                },
                {
                  name: 'templates',
                  label: '邮件模板',
                  type: 'array',
                  fields: [
                    {
                      name: 'type',
                      label: '模板类型',
                      type: 'select',
                      required: true,
                      options: [
                        { label: '欢迎邮件', value: 'welcome' },
                        { label: '邮箱验证', value: 'verification' },
                        { label: '密码重置', value: 'password_reset' },
                        { label: '订单通知', value: 'order_notification' },
                        { label: '支付通知', value: 'payment_notification' },
                      ],
                    },
                    {
                      name: 'subject',
                      label: '邮件主题',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'content',
                      label: '邮件内容',
                      type: 'richText',
                      required: true,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: '安全设置',
          fields: [
            {
              name: 'securitySettings',
              label: '安全设置',
              type: 'group',
              fields: [
                {
                  name: 'passwordPolicy',
                  label: '密码策略',
                  type: 'group',
                  fields: [
                    {
                      name: 'minLength',
                      label: '最小长度',
                      type: 'number',
                      defaultValue: 6,
                      min: 4,
                    },
                    {
                      name: 'requireUppercase',
                      label: '需要大写字母',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                    {
                      name: 'requireNumbers',
                      label: '需要数字',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                    {
                      name: 'requireSpecialChars',
                      label: '需要特殊字符',
                      type: 'checkbox',
                      defaultValue: false,
                    },
                  ],
                },
                {
                  name: 'loginAttempts',
                  label: '登录尝试限制',
                  type: 'group',
                  fields: [
                    {
                      name: 'maxAttempts',
                      label: '最大尝试次数',
                      type: 'number',
                      defaultValue: 5,
                      min: 1,
                    },
                    {
                      name: 'lockoutDuration',
                      label: '锁定时长(分钟)',
                      type: 'number',
                      defaultValue: 30,
                      min: 1,
                    },
                  ],
                },
                {
                  name: 'sessionTimeout',
                  label: '会话超时(小时)',
                  type: 'number',
                  defaultValue: 24,
                  min: 1,
                },
              ],
            },
          ],
        },
        {
          label: '其他设置',
          fields: [
            {
              name: 'maintenanceMode',
              label: '维护模式',
              type: 'group',
              fields: [
                {
                  name: 'enabled',
                  label: '启用维护模式',
                  type: 'checkbox',
                  defaultValue: false,
                },
                {
                  name: 'message',
                  label: '维护提示信息',
                  type: 'richText',
                  admin: {
                    condition: (data, siblingData) => siblingData?.enabled,
                  },
                },
                {
                  name: 'allowedIPs',
                  label: '允许访问的IP',
                  type: 'text',
                  hasMany: true,
                  admin: {
                    condition: (data, siblingData) => siblingData?.enabled,
                    description: '维护期间允许访问的IP地址',
                  },
                },
              ],
            },
            {
              name: 'analytics',
              label: '统计分析',
              type: 'group',
              fields: [
                {
                  name: 'googleAnalyticsId',
                  label: 'Google Analytics ID',
                  type: 'text',
                },
                {
                  name: 'baiduAnalyticsId',
                  label: '百度统计ID',
                  type: 'text',
                },
                {
                  name: 'enableHeatmap',
                  label: '启用热力图',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
            {
              name: 'customCode',
              label: '自定义代码',
              type: 'group',
              fields: [
                {
                  name: 'headerCode',
                  label: 'Header代码',
                  type: 'code',
                  admin: {
                    language: 'html',
                    description: '插入到<head>标签中的代码',
                  },
                },
                {
                  name: 'footerCode',
                  label: 'Footer代码',
                  type: 'code',
                  admin: {
                    language: 'html',
                    description: '插入到</body>标签前的代码',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
